<?php

declare(strict_types=1);

/*
 * QR Code generator library (PHP)
 * 
 * Copyright (c) Project Nayuki. (MIT License)
 * https://www.nayuki.io/page/qr-code-generator-library
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * - The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 * - The Software is provided "as is", without warranty of any kind, express or
 *   implied, including but not limited to the warranties of merchantability,
 *   fitness for a particular purpose and noninfringement. In no event shall the
 *   authors or copyright holders be liable for any claim, damages or other
 *   liability, whether in an action of contract, tort or otherwise, arising from,
 *   out of or in connection with the Software or the use or other dealings in the
 *   Software.
 */

namespace QrCodeGen;

use function QrCodeGen\appendBits;

/**
 * A segment of character/binary/control data in a QR Code symbol.
 * 
 * Instances of this class are immutable.
 * The mid-level way to create a segment is to take the payload data
 * and call a static factory function such as QrSegment::makeNumeric().
 * The low-level way to create a segment is to custom-make the bit buffer
 * and call the QrSegment() constructor with appropriate values.
 * This segment class imposes no length restrictions, but QR Codes have restrictions.
 * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.
 * Any segment longer than this is meaningless for the purpose of generating QR Codes.
 */
class QrSegment
{
    /**
     * Describes precisely all strings that are encodable in numeric mode.
     */
    private const NUMERIC_REGEX = '/^[0-9]*$/';
    
    /**
     * Describes precisely all strings that are encodable in alphanumeric mode.
     */
    private const ALPHANUMERIC_REGEX = '/^[A-Z0-9 $%*+.\/:+-]*$/';
    
    /**
     * The set of all legal characters in alphanumeric mode,
     * where each character value maps to the index in the string.
     */
    private const ALPHANUMERIC_CHARSET = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:';

    /**
     * Creates a new QR Code segment with the given attributes and data.
     * 
     * @param Mode $mode The mode indicator of this segment
     * @param int $numChars The length of this segment's unencoded data
     * @param array<int> $bitData The data bits of this segment
     */
    public function __construct(
        public readonly Mode $mode,
        public readonly int $numChars,
        private readonly array $bitData
    ) {
        if ($numChars < 0) {
            throw new \InvalidArgumentException("Invalid argument");
        }
    }

    /**
     * Returns a new copy of the data bits of this segment.
     * 
     * @return array<int> A copy of the bit data
     */
    public function getData(): array
    {
        return $this->bitData;
    }

    /**
     * Returns a segment representing the given binary data encoded in byte mode.
     * 
     * @param array<int> $data The binary data (array of bytes 0-255)
     * @return self A new QrSegment in byte mode
     */
    public static function makeBytes(array $data): self
    {
        $bb = [];
        foreach ($data as $b) {
            appendBits($b, 8, $bb);
        }
        return new self(Mode::BYTE, count($data), $bb);
    }

    /**
     * Returns a segment representing the given string of decimal digits encoded in numeric mode.
     * 
     * @param string $digits The numeric string (digits 0-9 only)
     * @return self A new QrSegment in numeric mode
     * @throws \InvalidArgumentException If string contains non-numeric characters
     */
    public static function makeNumeric(string $digits): self
    {
        if (!self::isNumeric($digits)) {
            throw new \InvalidArgumentException("String contains non-numeric characters");
        }
        
        $bb = [];
        for ($i = 0; $i < strlen($digits); ) {
            $n = min(strlen($digits) - $i, 3);
            appendBits(intval(substr($digits, $i, $n)), $n * 3 + 1, $bb);
            $i += $n;
        }
        return new self(Mode::NUMERIC, strlen($digits), $bb);
    }

    /**
     * Returns a segment representing the given text string encoded in alphanumeric mode.
     * 
     * @param string $text The alphanumeric text
     * @return self A new QrSegment in alphanumeric mode
     * @throws \InvalidArgumentException If string contains unencodable characters
     */
    public static function makeAlphanumeric(string $text): self
    {
        if (!self::isAlphanumeric($text)) {
            throw new \InvalidArgumentException("String contains unencodable characters in alphanumeric mode");
        }
        
        $bb = [];
        $i = 0;
        for ($i = 0; $i + 2 <= strlen($text); $i += 2) {
            $temp = strpos(self::ALPHANUMERIC_CHARSET, $text[$i]) * 45;
            $temp += strpos(self::ALPHANUMERIC_CHARSET, $text[$i + 1]);
            appendBits($temp, 11, $bb);
        }
        if ($i < strlen($text)) {
            appendBits(strpos(self::ALPHANUMERIC_CHARSET, $text[$i]), 6, $bb);
        }
        return new self(Mode::ALPHANUMERIC, strlen($text), $bb);
    }

    /**
     * Returns a new mutable list of zero or more segments to represent the given Unicode text string.
     * The result may use various segment modes and switch modes to optimize the length of the bit stream.
     *
     * @param string $text The text to encode
     * @return array<self> Array of QrSegment objects
     */
    public static function makeSegments(string $text): array
    {
        if ($text === '') {
            return [];
        } elseif (self::isNumeric($text)) {
            return [self::makeNumeric($text)];
        } elseif (self::isAlphanumeric($text)) {
            return [self::makeAlphanumeric($text)];
        } else {
            return [self::makeBytes(self::toUtf8ByteArray($text))];
        }
    }

    /**
     * Returns a segment representing an Extended Channel Interpretation (ECI) designator.
     *
     * @param int $assignVal The ECI assignment value
     * @return self A new QrSegment in ECI mode
     * @throws \InvalidArgumentException If assignment value is out of range
     */
    public static function makeEci(int $assignVal): self
    {
        $bb = [];
        if ($assignVal < 0) {
            throw new \InvalidArgumentException("ECI assignment value out of range");
        } elseif ($assignVal < (1 << 7)) {
            appendBits($assignVal, 8, $bb);
        } elseif ($assignVal < (1 << 14)) {
            appendBits(0b10, 2, $bb);
            appendBits($assignVal, 14, $bb);
        } elseif ($assignVal < 1000000) {
            appendBits(0b110, 3, $bb);
            appendBits($assignVal, 21, $bb);
        } else {
            throw new \InvalidArgumentException("ECI assignment value out of range");
        }
        return new self(Mode::ECI, 0, $bb);
    }

    /**
     * Tests whether the given string can be encoded as a segment in numeric mode.
     *
     * @param string $text The text to test
     * @return bool True if encodable in numeric mode
     */
    public static function isNumeric(string $text): bool
    {
        return preg_match(self::NUMERIC_REGEX, $text) === 1;
    }

    /**
     * Tests whether the given string can be encoded as a segment in alphanumeric mode.
     *
     * @param string $text The text to test
     * @return bool True if encodable in alphanumeric mode
     */
    public static function isAlphanumeric(string $text): bool
    {
        return preg_match(self::ALPHANUMERIC_REGEX, $text) === 1;
    }

    /**
     * Calculates and returns the number of bits needed to encode the given segments at the given version.
     *
     * @param array<self> $segs The segments to calculate bits for
     * @param int $version The QR Code version (1-40)
     * @return int|float The total bits needed, or INF if a segment has too many characters
     */
    public static function getTotalBits(array $segs, int $version): int|float
    {
        $result = 0;
        foreach ($segs as $seg) {
            $ccbits = $seg->mode->getNumCharCountBits($version);
            if ($seg->numChars >= (1 << $ccbits)) {
                return INF;
            }
            $result += 4 + $ccbits + count($seg->bitData);
        }
        return $result;
    }

    /**
     * Returns a new array of bytes representing the given string encoded in UTF-8.
     *
     * @param string $str The string to encode
     * @return array<int> Array of UTF-8 bytes
     */
    private static function toUtf8ByteArray(string $str): array
    {
        $bytes = [];
        $len = strlen($str);
        for ($i = 0; $i < $len; $i++) {
            $bytes[] = ord($str[$i]);
        }
        return $bytes;
    }
}
