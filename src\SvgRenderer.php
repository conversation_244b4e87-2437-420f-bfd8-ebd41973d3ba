<?php

declare(strict_types=1);

/*
 * QR Code generator library (PHP)
 * 
 * Copyright (c) Project Nayuki. (MIT License)
 * https://www.nayuki.io/page/qr-code-generator-library
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * - The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 * - The Software is provided "as is", without warranty of any kind, express or
 *   implied, including but not limited to the warranties of merchantability,
 *   fitness for a particular purpose and noninfringement. In no event shall the
 *   authors or copyright holders be liable for any claim, damages or other
 *   liability, whether in an action of contract, tort or otherwise, arising from,
 *   out of or in connection with the Software or the use or other dealings in the
 *   Software.
 */

namespace QrCodeGen;

/**
 * Enhanced SVG renderer for QR codes with advanced styling options.
 * 
 * This class provides various SVG rendering methods with support for
 * custom styling, gradients, rounded corners, and other visual enhancements.
 * 
 * @package QrCodeGen
 * <AUTHOR> Code Generator PHP Port
 * @since 1.1.0
 */
class SvgRenderer
{
    /**
     * Rendering style: individual rectangles for each module.
     */
    public const STYLE_RECTANGLES = 'rectangles';
    
    /**
     * Rendering style: single path with all modules.
     */
    public const STYLE_PATH = 'path';
    
    /**
     * Rendering style: rounded rectangles for each module.
     */
    public const STYLE_ROUNDED = 'rounded';
    
    /**
     * Rendering style: circular dots for each module.
     */
    public const STYLE_DOTS = 'dots';

    private QrCode $qrCode;
    private string $style;
    private array $options;

    /**
     * Creates a new SVG renderer.
     *
     * @param QrCode $qrCode The QR code to render
     * @param string $style Rendering style (use STYLE_* constants)
     * @param array $options Additional rendering options
     */
    public function __construct(QrCode $qrCode, string $style = self::STYLE_PATH, array $options = [])
    {
        $this->qrCode = $qrCode;
        $this->style = $style;
        $this->options = array_merge($this->getDefaultOptions(), $options);
    }

    /**
     * Renders the QR code as SVG.
     *
     * @param int $border Border size in modules
     * @param string $lightColor Color for light modules
     * @param string $darkColor Color for dark modules
     * @return string Complete SVG markup
     */
    public function render(
        int $border = 4,
        string $lightColor = '#FFFFFF',
        string $darkColor = '#000000'
    ): string {
        $size = $this->qrCode->size + $border * 2;
        
        $svg = $this->generateSvgHeader($size);
        $svg .= $this->generateBackground($size, $lightColor);
        $svg .= $this->generateModules($border, $darkColor);
        $svg .= $this->generateSvgFooter();
        
        return $svg;
    }

    /**
     * Renders QR code with gradient fill.
     *
     * @param int $border Border size in modules
     * @param string $lightColor Color for light modules
     * @param string $gradientStart Gradient start color
     * @param string $gradientEnd Gradient end color
     * @param string $gradientDirection Gradient direction ('horizontal', 'vertical', 'diagonal')
     * @return string Complete SVG markup with gradient
     */
    public function renderWithGradient(
        int $border = 4,
        string $lightColor = '#FFFFFF',
        string $gradientStart = '#000000',
        string $gradientEnd = '#333333',
        string $gradientDirection = 'diagonal'
    ): string {
        $size = $this->qrCode->size + $border * 2;
        
        $svg = $this->generateSvgHeader($size);
        $svg .= $this->generateGradientDefinition($gradientStart, $gradientEnd, $gradientDirection);
        $svg .= $this->generateBackground($size, $lightColor);
        $svg .= $this->generateModules($border, 'url(#qr-gradient)');
        $svg .= $this->generateSvgFooter();
        
        return $svg;
    }

    /**
     * Renders QR code with custom module shapes.
     *
     * @param int $border Border size in modules
     * @param string $lightColor Color for light modules
     * @param string $darkColor Color for dark modules
     * @param float $cornerRadius Corner radius for rounded styles (0.0-0.5)
     * @return string Complete SVG markup with custom shapes
     */
    public function renderWithCustomShapes(
        int $border = 4,
        string $lightColor = '#FFFFFF',
        string $darkColor = '#000000',
        float $cornerRadius = 0.2
    ): string {
        $size = $this->qrCode->size + $border * 2;
        
        $svg = $this->generateSvgHeader($size);
        $svg .= $this->generateBackground($size, $lightColor);
        
        switch ($this->style) {
            case self::STYLE_ROUNDED:
                $svg .= $this->generateRoundedModules($border, $darkColor, $cornerRadius);
                break;
            case self::STYLE_DOTS:
                $svg .= $this->generateDotModules($border, $darkColor);
                break;
            default:
                $svg .= $this->generateModules($border, $darkColor);
        }
        
        $svg .= $this->generateSvgFooter();
        
        return $svg;
    }

    /**
     * Gets the default rendering options.
     *
     * @return array Default options
     */
    private function getDefaultOptions(): array
    {
        return [
            'responsive' => false,
            'title' => null,
            'description' => null,
            'class' => null,
            'id' => null,
        ];
    }

    /**
     * Generates SVG header.
     *
     * @param int $size Total SVG size
     * @return string SVG header markup
     */
    private function generateSvgHeader(int $size): string
    {
        $svg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $svg .= "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n";
        
        $attributes = [
            'xmlns' => 'http://www.w3.org/2000/svg',
            'version' => '1.1',
            'viewBox' => "0 0 $size $size",
            'stroke' => 'none',
        ];
        
        if (!$this->options['responsive']) {
            $attributes['width'] = $size;
            $attributes['height'] = $size;
        }
        
        if ($this->options['class']) {
            $attributes['class'] = $this->options['class'];
        }
        
        if ($this->options['id']) {
            $attributes['id'] = $this->options['id'];
        }
        
        $svg .= '<svg';
        foreach ($attributes as $name => $value) {
            $svg .= " $name=\"$value\"";
        }
        $svg .= ">\n";
        
        if ($this->options['title']) {
            $svg .= "\t<title>" . htmlspecialchars($this->options['title']) . "</title>\n";
        }
        
        if ($this->options['description']) {
            $svg .= "\t<desc>" . htmlspecialchars($this->options['description']) . "</desc>\n";
        }
        
        return $svg;
    }

    /**
     * Generates SVG footer.
     *
     * @return string SVG footer markup
     */
    private function generateSvgFooter(): string
    {
        return "</svg>\n";
    }

    /**
     * Generates background rectangle.
     *
     * @param int $size Total size
     * @param string $color Background color
     * @return string Background SVG markup
     */
    private function generateBackground(int $size, string $color): string
    {
        return "\t<rect width=\"100%\" height=\"100%\" fill=\"$color\"/>\n";
    }

    /**
     * Generates QR code modules based on current style.
     *
     * @param int $border Border size
     * @param string $color Module color
     * @return string Modules SVG markup
     */
    private function generateModules(int $border, string $color): string
    {
        return match ($this->style) {
            self::STYLE_RECTANGLES => $this->generateRectangleModules($border, $color),
            self::STYLE_PATH => $this->generatePathModules($border, $color),
            self::STYLE_ROUNDED => $this->generateRoundedModules($border, $color, 0.2),
            self::STYLE_DOTS => $this->generateDotModules($border, $color),
            default => $this->generatePathModules($border, $color),
        };
    }

    /**
     * Generates modules as individual rectangles.
     *
     * @param int $border Border size
     * @param string $color Module color
     * @return string Rectangle modules SVG markup
     */
    private function generateRectangleModules(int $border, string $color): string
    {
        $svg = '';
        
        for ($y = 0; $y < $this->qrCode->size; $y++) {
            for ($x = 0; $x < $this->qrCode->size; $x++) {
                if ($this->qrCode->getModule($x, $y)) {
                    $px = $x + $border;
                    $py = $y + $border;
                    $svg .= "\t<rect x=\"$px\" y=\"$py\" width=\"1\" height=\"1\" fill=\"$color\"/>\n";
                }
            }
        }
        
        return $svg;
    }

    /**
     * Generates modules as a single path.
     *
     * @param int $border Border size
     * @param string $color Module color
     * @return string Path modules SVG markup
     */
    private function generatePathModules(int $border, string $color): string
    {
        $svg = "\t<path d=\"";
        
        for ($y = 0; $y < $this->qrCode->size; $y++) {
            for ($x = 0; $x < $this->qrCode->size; $x++) {
                if ($this->qrCode->getModule($x, $y)) {
                    $px = $x + $border;
                    $py = $y + $border;
                    $svg .= "M$px,$py h1v1h-1z";
                }
            }
        }
        
        $svg .= "\" fill=\"$color\"/>\n";
        
        return $svg;
    }

    /**
     * Generates modules as rounded rectangles.
     *
     * @param int $border Border size
     * @param string $color Module color
     * @param float $cornerRadius Corner radius (0.0-0.5)
     * @return string Rounded modules SVG markup
     */
    private function generateRoundedModules(int $border, string $color, float $cornerRadius): string
    {
        $svg = '';
        $radius = max(0, min(0.5, $cornerRadius));
        
        for ($y = 0; $y < $this->qrCode->size; $y++) {
            for ($x = 0; $x < $this->qrCode->size; $x++) {
                if ($this->qrCode->getModule($x, $y)) {
                    $px = $x + $border;
                    $py = $y + $border;
                    $svg .= "\t<rect x=\"$px\" y=\"$py\" width=\"1\" height=\"1\" rx=\"$radius\" ry=\"$radius\" fill=\"$color\"/>\n";
                }
            }
        }
        
        return $svg;
    }

    /**
     * Generates modules as circular dots.
     *
     * @param int $border Border size
     * @param string $color Module color
     * @return string Dot modules SVG markup
     */
    private function generateDotModules(int $border, string $color): string
    {
        $svg = '';
        $radius = 0.4; // Slightly smaller than full module
        
        for ($y = 0; $y < $this->qrCode->size; $y++) {
            for ($x = 0; $x < $this->qrCode->size; $x++) {
                if ($this->qrCode->getModule($x, $y)) {
                    $cx = $x + $border + 0.5;
                    $cy = $y + $border + 0.5;
                    $svg .= "\t<circle cx=\"$cx\" cy=\"$cy\" r=\"$radius\" fill=\"$color\"/>\n";
                }
            }
        }
        
        return $svg;
    }

    /**
     * Generates gradient definition.
     *
     * @param string $startColor Gradient start color
     * @param string $endColor Gradient end color
     * @param string $direction Gradient direction
     * @return string Gradient definition SVG markup
     */
    private function generateGradientDefinition(
        string $startColor,
        string $endColor,
        string $direction
    ): string {
        $coords = match ($direction) {
            'horizontal' => ['x1' => '0%', 'y1' => '0%', 'x2' => '100%', 'y2' => '0%'],
            'vertical' => ['x1' => '0%', 'y1' => '0%', 'x2' => '0%', 'y2' => '100%'],
            'diagonal' => ['x1' => '0%', 'y1' => '0%', 'x2' => '100%', 'y2' => '100%'],
            default => ['x1' => '0%', 'y1' => '0%', 'x2' => '100%', 'y2' => '100%'],
        };
        
        $svg = "\t<defs>\n";
        $svg .= "\t\t<linearGradient id=\"qr-gradient\" x1=\"{$coords['x1']}\" y1=\"{$coords['y1']}\" x2=\"{$coords['x2']}\" y2=\"{$coords['y2']}\">\n";
        $svg .= "\t\t\t<stop offset=\"0%\" stop-color=\"$startColor\"/>\n";
        $svg .= "\t\t\t<stop offset=\"100%\" stop-color=\"$endColor\"/>\n";
        $svg .= "\t\t</linearGradient>\n";
        $svg .= "\t</defs>\n";
        
        return $svg;
    }
}
