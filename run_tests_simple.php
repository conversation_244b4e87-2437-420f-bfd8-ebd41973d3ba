<?php

declare(strict_types=1);

/**
 * Simple test runner for logo functionality without PHPUnit
 */

// Manual includes
require_once 'src/functions.php';
require_once 'src/Ecc.php';
require_once 'src/Mode.php';
require_once 'src/QrSegment.php';
require_once 'src/QrCode.php';
require_once 'src/Exceptions/LogoException.php';
require_once 'src/LogoConfig.php';
require_once 'src/QrCodeLogo.php';
require_once 'src/SvgRenderer.php';

use QrCodeGen\QrCode;
use QrCodeGen\QrCodeLogo;
use QrCodeGen\LogoConfig;
use QrCodeGen\SvgRenderer;
use QrCodeGen\Ecc;
use QrCodeGen\Exceptions\LogoException;

echo "Simple Test Suite for QR Code Logo Feature\n";
echo "==========================================\n\n";

$testsPassed = 0;
$testsFailed = 0;

function runTest(string $testName, callable $test): void {
    global $testsPassed, $testsFailed;
    
    echo "Running: $testName\n";
    try {
        $result = $test();
        if ($result === true) {
            echo "  ✓ PASSED\n";
            $testsPassed++;
        } else {
            echo "  ✗ FAILED: $result\n";
            $testsFailed++;
        }
    } catch (Exception $e) {
        echo "  ✗ FAILED: Exception - {$e->getMessage()}\n";
        $testsFailed++;
    }
    echo "\n";
}

// Test 1: LogoConfig basic functionality
runTest("LogoConfig - Basic Construction", function() {
    $svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="blue"/></svg>';
    $config = new LogoConfig($svg, 0.20);
    
    if ($config->getLogoSource() !== $svg) return "Logo source mismatch";
    if ($config->getSizePercentage() !== 0.20) return "Size percentage mismatch";
    if (!$config->shouldAddBackground()) return "Background should be enabled by default";
    if ($config->getBackgroundColor() !== '#FFFFFF') return "Background color mismatch";
    
    return true;
});

// Test 2: LogoConfig validation
runTest("LogoConfig - Size Validation", function() {
    $svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="blue"/></svg>';
    
    try {
        new LogoConfig($svg, 1.5); // Invalid size
        return "Should have thrown exception for invalid size";
    } catch (LogoException $e) {
        if ($e->getCode() !== LogoException::INVALID_CONFIGURATION) {
            return "Wrong exception code";
        }
    }
    
    return true;
});

// Test 3: LogoConfig ECC validation
runTest("LogoConfig - ECC Level Validation", function() {
    $svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="blue"/></svg>';
    $config = new LogoConfig($svg, 0.25);
    
    try {
        $config->validateForErrorCorrectionLevel(Ecc::LOW); // 0.25 > 0.15 (max for LOW)
        return "Should have thrown exception for size too large for LOW ECC";
    } catch (LogoException $e) {
        if ($e->getCode() !== LogoException::SIZE_CONSTRAINT_VIOLATION) {
            return "Wrong exception code";
        }
    }
    
    return true;
});

// Test 4: QrCodeLogo basic functionality
runTest("QrCodeLogo - Basic Construction", function() {
    $segs = \QrCodeGen\QrSegment::makeSegments("Test");
    $qr = QrCode::encodeSegments($segs, Ecc::MEDIUM, 1, 40, -1, false);
    $svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="blue"/></svg>';
    $config = new LogoConfig($svg, 0.15);
    
    $qrWithLogo = new QrCodeLogo($qr, $config);
    
    if ($qrWithLogo->getQrCode() !== $qr) return "QR code reference mismatch";
    if ($qrWithLogo->getConfig() !== $config) return "Config reference mismatch";
    
    return true;
});

// Test 5: QrCodeLogo SVG generation
runTest("QrCodeLogo - SVG Generation", function() {
    $segs = \QrCodeGen\QrSegment::makeSegments("Test");
    $qr = QrCode::encodeSegments($segs, Ecc::MEDIUM, 1, 40, -1, false);
    $svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="blue"/></svg>';
    $config = new LogoConfig($svg, 0.15);
    
    $qrWithLogo = new QrCodeLogo($qr, $config);
    $output = $qrWithLogo->toSvg();
    
    if (!str_contains($output, '<?xml version="1.0"')) return "Missing XML declaration";
    if (!str_contains($output, '<svg xmlns="http://www.w3.org/2000/svg"')) return "Missing SVG element";
    if (!str_contains($output, '<path d="')) return "Missing QR code path";
    if (!str_contains($output, '<g transform="translate(')) return "Missing logo transform";
    if (!str_contains($output, '</svg>')) return "Missing SVG closing tag";
    
    return true;
});

// Test 6: SvgRenderer basic functionality
runTest("SvgRenderer - Basic Rendering", function() {
    $segs = \QrCodeGen\QrSegment::makeSegments("Test");
    $qr = QrCode::encodeSegments($segs, Ecc::MEDIUM, 1, 40, -1, false);
    
    $renderer = new SvgRenderer($qr);
    $output = $renderer->render();
    
    if (!str_contains($output, '<?xml version="1.0"')) return "Missing XML declaration";
    if (!str_contains($output, '<svg xmlns="http://www.w3.org/2000/svg"')) return "Missing SVG element";
    if (!str_contains($output, '<path d="')) return "Missing QR code path";
    if (!str_contains($output, '</svg>')) return "Missing SVG closing tag";
    
    return true;
});

// Test 7: SvgRenderer different styles
runTest("SvgRenderer - Different Styles", function() {
    $segs = \QrCodeGen\QrSegment::makeSegments("Test");
    $qr = QrCode::encodeSegments($segs, Ecc::MEDIUM, 1, 40, -1, false);
    
    // Test rectangles style
    $renderer = new SvgRenderer($qr, SvgRenderer::STYLE_RECTANGLES);
    $output = $renderer->render();
    if (!str_contains($output, '<rect x=')) return "Rectangles style missing rect elements";
    
    // Test dots style
    $renderer = new SvgRenderer($qr, SvgRenderer::STYLE_DOTS);
    $output = $renderer->render();
    if (!str_contains($output, '<circle cx=')) return "Dots style missing circle elements";
    
    return true;
});

// Test 8: Error correction level limits
runTest("Error Correction Level Limits", function() {
    $svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="blue"/></svg>';

    $limits = [
        ['ecc' => Ecc::LOW, 'size' => 0.15],
        ['ecc' => Ecc::MEDIUM, 'size' => 0.20],
        ['ecc' => Ecc::QUARTILE, 'size' => 0.25],
        ['ecc' => Ecc::HIGH, 'size' => 0.30],
    ];

    foreach ($limits as $limit) {
        $ecc = $limit['ecc'];
        $maxSize = $limit['size'];
        if (LogoConfig::getMaxLogoSize($ecc) !== $maxSize) {
            return "Wrong max size for {$ecc->name}: expected $maxSize, got " . LogoConfig::getMaxLogoSize($ecc);
        }
    }

    return true;
});

// Test 9: SVG content validation
runTest("SVG Content Validation", function() {
    // Test invalid SVG (missing svg tags)
    try {
        $config = new LogoConfig('<svg><circle cx="50" cy="50" r="40" fill="blue"/>'); // Missing closing </svg>
        $config->getLogoSvgContent();
        return "Should have rejected invalid SVG";
    } catch (LogoException $e) {
        if ($e->getCode() !== LogoException::INVALID_SVG) {
            return "Wrong exception code for invalid SVG: got {$e->getCode()}, expected " . LogoException::INVALID_SVG;
        }
    }

    // Test dangerous SVG
    try {
        $config = new LogoConfig('<svg xmlns="http://www.w3.org/2000/svg"><script>alert("xss")</script></svg>');
        $config->getLogoSvgContent();
        return "Should have rejected dangerous SVG";
    } catch (LogoException $e) {
        if ($e->getCode() !== LogoException::INVALID_SVG) {
            return "Wrong exception code for dangerous SVG: got {$e->getCode()}, expected " . LogoException::INVALID_SVG;
        }
    }

    return true;
});

// Test 10: Complex logo integration
runTest("Complex Logo Integration", function() {
    $segs = \QrCodeGen\QrSegment::makeSegments("Complex test");
    $qr = QrCode::encodeSegments($segs, Ecc::HIGH, 1, 40, -1, false);
    
    $complexSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
        <defs>
            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#6f42c1;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#e83e8c;stop-opacity:1" />
            </linearGradient>
        </defs>
        <circle cx="100" cy="100" r="80" fill="url(#grad1)"/>
        <text x="100" y="110" text-anchor="middle" fill="white" font-family="Arial" font-size="24">LOGO</text>
    </svg>';
    
    $config = new LogoConfig($complexSvg, 0.25);
    $qrWithLogo = new QrCodeLogo($qr, $config);
    $output = $qrWithLogo->toSvg();
    
    if (!str_contains($output, 'linearGradient')) return "Missing gradient definition";
    if (!str_contains($output, 'circle')) return "Missing circle element";
    if (!str_contains($output, 'text')) return "Missing text element";
    
    return true;
});

// Run all tests
echo "Test Results:\n";
echo "=============\n";
echo "Tests Passed: $testsPassed\n";
echo "Tests Failed: $testsFailed\n";
echo "Total Tests: " . ($testsPassed + $testsFailed) . "\n";

if ($testsFailed === 0) {
    echo "\n🎉 All tests passed!\n";
    exit(0);
} else {
    echo "\n❌ Some tests failed.\n";
    exit(1);
}
