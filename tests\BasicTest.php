<?php

declare(strict_types=1);

namespace QrCodeGen\Tests;

use PHPUnit\Framework\TestCase;
use QrCodeGen\Ecc;
use QrCodeGen\Mode;
use QrCodeGen\QrSegment;

/**
 * Basic tests for the QR Code library foundation.
 */
class BasicTest extends TestCase
{
    public function testEccEnum(): void
    {
        $this->assertSame(0, Ecc::LOW->getOrdinal());
        $this->assertSame(1, Ecc::MEDIUM->getOrdinal());
        $this->assertSame(2, Ecc::QUARTILE->getOrdinal());
        $this->assertSame(3, Ecc::HIGH->getOrdinal());
        
        $this->assertSame(1, Ecc::LOW->getFormatBits());
        $this->assertSame(0, Ecc::MEDIUM->getFormatBits());
        $this->assertSame(3, Ecc::QUARTILE->getFormatBits());
        $this->assertSame(2, Ecc::HIGH->getFormatBits());
    }

    public function testModeEnum(): void
    {
        $this->assertSame(0x1, Mode::NUMERIC->getModeBits());
        $this->assertSame(0x2, Mode::ALPHANUMERIC->getModeBits());
        $this->assertSame(0x4, Mode::BYTE->getModeBits());
        $this->assertSame(0x8, Mode::KANJI->getModeBits());
        $this->assertSame(0x7, Mode::ECI->getModeBits());
    }

    public function testModeCharCountBits(): void
    {
        // Test numeric mode character count bits for different version ranges
        $this->assertSame(10, Mode::NUMERIC->getNumCharCountBits(1));
        $this->assertSame(12, Mode::NUMERIC->getNumCharCountBits(10));
        $this->assertSame(14, Mode::NUMERIC->getNumCharCountBits(27));
        
        // Test alphanumeric mode
        $this->assertSame(9, Mode::ALPHANUMERIC->getNumCharCountBits(1));
        $this->assertSame(11, Mode::ALPHANUMERIC->getNumCharCountBits(10));
        $this->assertSame(13, Mode::ALPHANUMERIC->getNumCharCountBits(27));
        
        // Test byte mode
        $this->assertSame(8, Mode::BYTE->getNumCharCountBits(1));
        $this->assertSame(16, Mode::BYTE->getNumCharCountBits(10));
        $this->assertSame(16, Mode::BYTE->getNumCharCountBits(27));
    }

    public function testQrSegmentValidation(): void
    {
        // Test numeric validation
        $this->assertTrue(QrSegment::isNumeric('123456789'));
        $this->assertTrue(QrSegment::isNumeric('0'));
        $this->assertTrue(QrSegment::isNumeric(''));
        $this->assertFalse(QrSegment::isNumeric('123a'));
        $this->assertFalse(QrSegment::isNumeric('12.34'));
        
        // Test alphanumeric validation
        $this->assertTrue(QrSegment::isAlphanumeric('HELLO123'));
        $this->assertTrue(QrSegment::isAlphanumeric('ABC 123 $%*+-./:'));
        $this->assertFalse(QrSegment::isAlphanumeric('hello')); // lowercase not allowed
        $this->assertFalse(QrSegment::isAlphanumeric('ABC@123')); // @ not allowed
    }

    public function testQrSegmentCreation(): void
    {
        // Test numeric segment
        $numSeg = QrSegment::makeNumeric('123456');
        $this->assertSame(Mode::NUMERIC, $numSeg->mode);
        $this->assertSame(6, $numSeg->numChars);
        
        // Test alphanumeric segment
        $alphaSeg = QrSegment::makeAlphanumeric('HELLO');
        $this->assertSame(Mode::ALPHANUMERIC, $alphaSeg->mode);
        $this->assertSame(5, $alphaSeg->numChars);
        
        // Test byte segment
        $byteSeg = QrSegment::makeBytes([72, 101, 108, 108, 111]); // "Hello"
        $this->assertSame(Mode::BYTE, $byteSeg->mode);
        $this->assertSame(5, $byteSeg->numChars);
    }

    public function testQrSegmentMakeSegments(): void
    {
        // Test automatic mode selection
        $numSegs = QrSegment::makeSegments('123456');
        $this->assertCount(1, $numSegs);
        $this->assertSame(Mode::NUMERIC, $numSegs[0]->mode);
        
        $alphaSegs = QrSegment::makeSegments('HELLO123');
        $this->assertCount(1, $alphaSegs);
        $this->assertSame(Mode::ALPHANUMERIC, $alphaSegs[0]->mode);
        
        $byteSegs = QrSegment::makeSegments('Hello, world!');
        $this->assertCount(1, $byteSegs);
        $this->assertSame(Mode::BYTE, $byteSegs[0]->mode);
        
        // Test empty string
        $emptySegs = QrSegment::makeSegments('');
        $this->assertCount(0, $emptySegs);
    }

    public function testUtilityFunctions(): void
    {
        // Test appendBits function
        $bb = [];
        \QrCodeGen\appendBits(5, 3, $bb); // 5 = 101 in binary
        $this->assertSame([1, 0, 1], $bb);
        
        \QrCodeGen\appendBits(3, 2, $bb); // 3 = 11 in binary
        $this->assertSame([1, 0, 1, 1, 1], $bb);
        
        // Test getBit function
        $this->assertTrue(\QrCodeGen\getBit(5, 0)); // bit 0 of 5 (101) is 1
        $this->assertFalse(\QrCodeGen\getBit(5, 1)); // bit 1 of 5 (101) is 0
        $this->assertTrue(\QrCodeGen\getBit(5, 2)); // bit 2 of 5 (101) is 1
    }

    public function testInvalidInputs(): void
    {
        // Test invalid numeric string
        $this->expectException(\InvalidArgumentException::class);
        QrSegment::makeNumeric('123a');
    }
}
