<?php

declare(strict_types=1);

// Simple autoloader for testing without composer
spl_autoload_register(function ($class) {
    $prefix = 'QrCodeGen\\';
    $base_dir = __DIR__ . '/src/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

// Include functions
require_once __DIR__ . '/src/functions.php';

use QrCodeGen\QrCode;
use QrCodeGen\Ecc;

echo "Testing Reed-Solomon implementation...\n\n";

// Test Reed-Solomon multiplication
$reflection = new ReflectionClass(QrCode::class);
$multiplyMethod = $reflection->getMethod('reedSolomonMultiply');
$multiplyMethod->setAccessible(true);

echo "Testing Reed-Solomon multiplication:\n";
$result1 = $multiplyMethod->invoke(null, 3, 5);
echo "reedSolomonMultiply(3, 5) = $result1\n";

$result2 = $multiplyMethod->invoke(null, 0, 255);
echo "reedSolomonMultiply(0, 255) = $result2 (expected: 0)\n";

$result3 = $multiplyMethod->invoke(null, 1, 255);
echo "reedSolomonMultiply(1, 255) = $result3 (expected: 255)\n";

// Test Reed-Solomon divisor computation
$divisorMethod = $reflection->getMethod('reedSolomonComputeDivisor');
$divisorMethod->setAccessible(true);

echo "\nTesting Reed-Solomon divisor computation:\n";
$divisor = $divisorMethod->invoke(null, 7);
echo "Divisor for degree 7: [" . implode(', ', $divisor) . "]\n";
echo "Divisor length: " . count($divisor) . " (expected: 7)\n";

// Test Reed-Solomon remainder computation
$remainderMethod = $reflection->getMethod('reedSolomonComputeRemainder');
$remainderMethod->setAccessible(true);

echo "\nTesting Reed-Solomon remainder computation:\n";
$data = [32, 65, 205, 69, 41, 220, 46, 128, 236];
$remainder = $remainderMethod->invoke(null, $data, $divisor);
echo "Remainder for test data: [" . implode(', ', $remainder) . "]\n";
echo "Remainder length: " . count($remainder) . " (expected: 7)\n";

// Test data capacity calculation
echo "\nTesting data capacity calculations:\n";
$getNumDataMethod = $reflection->getMethod('getNumDataCodewords');
$getNumDataMethod->setAccessible(true);

$capacity1 = $getNumDataMethod->invoke(null, 1, Ecc::LOW);
echo "Version 1, LOW ECC data capacity: $capacity1 codewords\n";

$capacity2 = $getNumDataMethod->invoke(null, 1, Ecc::HIGH);
echo "Version 1, HIGH ECC data capacity: $capacity2 codewords\n";

$capacity3 = $getNumDataMethod->invoke(null, 40, Ecc::LOW);
echo "Version 40, LOW ECC data capacity: $capacity3 codewords\n";

echo "\nReed-Solomon tests completed!\n";
