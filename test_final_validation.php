<?php

declare(strict_types=1);

// Simple autoloader for testing without composer
spl_autoload_register(function ($class) {
    $prefix = 'QrCodeGen\\';
    $base_dir = __DIR__ . '/src/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

// Include functions
require_once __DIR__ . '/src/functions.php';

use QrCodeGen\QrCode;
use QrCodeGen\QrSegment;
use QrCodeGen\Ecc;

echo "QR Code Generator - Final Validation Test\n";
echo "=========================================\n\n";

$testCases = [
    // Basic test cases
    ["Hello, world!", Ecc::LOW],
    ["Hello, world!", Ecc::MEDIUM],
    ["Hello, world!", Ecc::QUARTILE],
    ["Hello, world!", Ecc::HIGH],
    
    // Numeric data
    ["123456789", Ecc::LOW],
    ["0123456789012345678901234567890", Ecc::MEDIUM],
    
    // Alphanumeric data
    ["HELLO WORLD", Ecc::LOW],
    ["PRODUCT-CODE-12345", Ecc::MEDIUM],
    ["ABC123 $%*+-./:ABC123", Ecc::HIGH],
    
    // URLs
    ["https://www.example.com", Ecc::LOW],
    ["https://www.example.com/path?param=value&other=123", Ecc::MEDIUM],
    
    // Mixed content
    ["Hello, 世界! 123", Ecc::HIGH],
    ["Email: <EMAIL>", Ecc::MEDIUM],
    
    // Edge cases
    ["", Ecc::LOW], // Empty string
    ["A", Ecc::HIGH], // Single character
    [str_repeat("A", 100), Ecc::LOW], // Longer text
];

$totalTests = 0;
$passedTests = 0;

foreach ($testCases as $i => [$text, $ecc]) {
    $totalTests++;
    
    try {
        echo "Test " . ($i + 1) . ": ";
        echo "Text: '" . (strlen($text) > 30 ? substr($text, 0, 30) . "..." : $text) . "' ";
        echo "ECC: {$ecc->name}\n";
        
        $qr = QrCode::encodeText($text, $ecc);
        
        // Validate basic properties
        if ($qr->version < 1 || $qr->version > 40) {
            throw new Exception("Invalid version: {$qr->version}");
        }
        
        if ($qr->size !== $qr->version * 4 + 17) {
            throw new Exception("Invalid size: {$qr->size} (expected: " . ($qr->version * 4 + 17) . ")");
        }
        
        if ($qr->mask < 0 || $qr->mask > 7) {
            throw new Exception("Invalid mask: {$qr->mask}");
        }
        
        if ($qr->errorCorrectionLevel !== $ecc) {
            // ECC might be boosted, which is allowed
            echo "  Note: ECC boosted from {$ecc->name} to {$qr->errorCorrectionLevel->name}\n";
        }
        
        // Validate finder patterns
        validateFinderPatterns($qr);
        
        // Validate timing patterns
        validateTimingPatterns($qr);
        
        // Validate format information
        validateFormatInfo($qr);
        
        echo "  ✓ Version: {$qr->version}, Size: {$qr->size}×{$qr->size}, Mask: {$qr->mask}\n";
        $passedTests++;
        
    } catch (Exception $e) {
        echo "  ✗ FAILED: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Test manual segments
echo "Testing manual segment creation...\n";
try {
    $segments = [
        QrSegment::makeNumeric("123"),
        QrSegment::makeAlphanumeric("ABC"),
        QrSegment::makeBytes([33, 34, 35]) // "!\"#"
    ];
    
    $qr = QrCode::encodeSegments($segments, Ecc::MEDIUM);
    echo "✓ Manual segments test passed\n";
    $totalTests++;
    $passedTests++;
} catch (Exception $e) {
    echo "✗ Manual segments test failed: " . $e->getMessage() . "\n";
    $totalTests++;
}

// Test version constraints
echo "\nTesting version constraints...\n";
try {
    $qr = QrCode::encodeSegments(
        QrSegment::makeSegments("Version constraint test"),
        Ecc::LOW,
        5, 10, -1, true
    );
    
    if ($qr->version < 5 || $qr->version > 10) {
        throw new Exception("Version {$qr->version} outside requested range 5-10");
    }
    
    echo "✓ Version constraints test passed (version: {$qr->version})\n";
    $totalTests++;
    $passedTests++;
} catch (Exception $e) {
    echo "✗ Version constraints test failed: " . $e->getMessage() . "\n";
    $totalTests++;
}

// Test error conditions
echo "\nTesting error conditions...\n";
$errorTests = [
    function() { QrSegment::makeNumeric("123a"); }, // Invalid numeric
    function() { QrSegment::makeAlphanumeric("hello"); }, // Invalid alphanumeric
    function() { QrCode::encodeText(str_repeat("A", 10000), Ecc::HIGH); }, // Data too long
];

foreach ($errorTests as $i => $test) {
    $totalTests++;
    try {
        $test();
        echo "✗ Error test " . ($i + 1) . " failed: Expected exception but none thrown\n";
    } catch (Exception $e) {
        echo "✓ Error test " . ($i + 1) . " passed: " . get_class($e) . "\n";
        $passedTests++;
    }
}

// Performance test
echo "\nPerformance test...\n";
$start = microtime(true);
for ($i = 0; $i < 100; $i++) {
    QrCode::encodeText("Performance test $i", Ecc::MEDIUM);
}
$end = microtime(true);
$duration = $end - $start;
echo "Generated 100 QR codes in " . number_format($duration, 3) . " seconds\n";
echo "Average: " . number_format($duration / 100 * 1000, 2) . " ms per QR code\n";

// Summary
echo "\n" . str_repeat("=", 50) . "\n";
echo "FINAL VALIDATION SUMMARY\n";
echo str_repeat("=", 50) . "\n";
echo "Total tests: $totalTests\n";
echo "Passed: $passedTests\n";
echo "Failed: " . ($totalTests - $passedTests) . "\n";
echo "Success rate: " . number_format($passedTests / $totalTests * 100, 1) . "%\n";

if ($passedTests === $totalTests) {
    echo "\n🎉 ALL TESTS PASSED! The QR Code library is working correctly.\n";
} else {
    echo "\n❌ Some tests failed. Please review the output above.\n";
}

function validateFinderPatterns(QrCode $qr): void
{
    // Check finder pattern corners (should be dark)
    $corners = [
        [0, 0], [6, 0], [0, 6], [6, 6], // Top-left
        [$qr->size - 7, 0], [$qr->size - 1, 0], [$qr->size - 7, 6], [$qr->size - 1, 6], // Top-right
        [0, $qr->size - 7], [6, $qr->size - 7], [0, $qr->size - 1], [6, $qr->size - 1], // Bottom-left
    ];
    
    foreach ($corners as [$x, $y]) {
        if (!$qr->getModule($x, $y)) {
            throw new Exception("Finder pattern corner at ($x, $y) should be dark");
        }
    }
}

function validateTimingPatterns(QrCode $qr): void
{
    // Check timing patterns (alternating pattern on row/column 6)
    for ($i = 8; $i < $qr->size - 8; $i++) {
        $expected = ($i % 2) === 0;
        if ($qr->getModule(6, $i) !== $expected) {
            throw new Exception("Horizontal timing pattern error at position $i");
        }
        if ($qr->getModule($i, 6) !== $expected) {
            throw new Exception("Vertical timing pattern error at position $i");
        }
    }
}

function validateFormatInfo(QrCode $qr): void
{
    // Check that format information areas are not all the same
    $formatBits = [];
    
    // Read format bits from first copy
    for ($i = 0; $i <= 5; $i++) {
        $formatBits[] = $qr->getModule(8, $i);
    }
    $formatBits[] = $qr->getModule(8, 7);
    $formatBits[] = $qr->getModule(8, 8);
    $formatBits[] = $qr->getModule(7, 8);
    for ($i = 9; $i < 15; $i++) {
        $formatBits[] = $qr->getModule(14 - $i, 8);
    }
    
    // Check that not all bits are the same (would indicate an error)
    $allSame = true;
    $firstBit = $formatBits[0];
    foreach ($formatBits as $bit) {
        if ($bit !== $firstBit) {
            $allSame = false;
            break;
        }
    }
    
    if ($allSame) {
        throw new Exception("Format information appears to be invalid (all bits same)");
    }
}

echo "\nLibrary validation completed!\n";
