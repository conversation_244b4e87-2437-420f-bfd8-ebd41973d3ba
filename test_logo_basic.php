<?php

declare(strict_types=1);

/**
 * Basic test for QR Code Logo functionality without composer autoloader
 */

// Manual includes
require_once 'src/functions.php';
require_once 'src/Ecc.php';
require_once 'src/Mode.php';
require_once 'src/QrSegment.php';
require_once 'src/QrCode.php';
require_once 'src/Exceptions/LogoException.php';
require_once 'src/LogoConfig.php';
require_once 'src/QrCodeLogo.php';
require_once 'src/SvgRenderer.php';

use QrCodeGen\QrCode;
use QrCodeGen\QrCodeLogo;
use QrCodeGen\LogoConfig;
use QrCodeGen\SvgRenderer;
use QrCodeGen\Ecc;
use QrCodeGen\Exceptions\LogoException;

echo "QR Code Logo Feature Test\n";
echo "=========================\n\n";

try {
    // Test 1: Basic QR Code generation
    echo "Test 1: Basic QR Code Generation\n";
    echo "--------------------------------\n";
    
    $qr = QrCode::encodeText("https://example.com/test", Ecc::MEDIUM);
    echo "✓ QR Code generated successfully\n";
    echo "  Size: {$qr->size}×{$qr->size} modules\n";
    echo "  Error Correction: {$qr->errorCorrectionLevel->name}\n\n";
    
    // Test 2: Logo Configuration
    echo "Test 2: Logo Configuration\n";
    echo "--------------------------\n";
    
    $logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
        <circle cx="50" cy="50" r="40" fill="#007bff"/>
        <text x="50" y="55" text-anchor="middle" fill="white" font-family="Arial" font-size="20" font-weight="bold">QR</text>
    </svg>';
    
    $logoConfig = new LogoConfig(
        logoSource: $logoSvg,
        sizePercentage: 0.15,
        addBackground: true,
        backgroundColor: '#FFFFFF'
    );
    
    echo "✓ Logo configuration created successfully\n";
    echo "  Logo size: 15% of QR code\n";
    echo "  Background: Enabled (white)\n\n";
    
    // Test 3: QR Code with Logo
    echo "Test 3: QR Code with Logo\n";
    echo "-------------------------\n";
    
    $qrWithLogo = new QrCodeLogo($qr, $logoConfig);
    echo "✓ QR Code with logo created successfully\n";
    
    // Test 4: SVG Generation
    echo "Test 4: SVG Generation\n";
    echo "----------------------\n";
    
    $svg = $qrWithLogo->toSvg();
    echo "✓ SVG generated successfully\n";
    echo "  SVG length: " . strlen($svg) . " characters\n";
    
    // Save SVG file
    file_put_contents('test_logo_output.svg', $svg);
    echo "✓ SVG saved to test_logo_output.svg\n\n";
    
    // Test 5: Error Correction Level Validation
    echo "Test 5: Error Correction Level Validation\n";
    echo "-----------------------------------------\n";
    
    try {
        $lowEccQr = QrCode::encodeText("Test", Ecc::LOW);
        $largeLogo = new LogoConfig($logoSvg, sizePercentage: 0.25); // Too large for LOW ECC (max is 0.15)
        $invalidQrWithLogo = new QrCodeLogo($lowEccQr, $largeLogo);
        echo "✗ Should have failed but didn't\n";
    } catch (LogoException $e) {
        echo "✓ Correctly caught size constraint violation\n";
        echo "  Error: {$e->getMessage()}\n";
    }
    
    echo "\n";
    
    // Test 6: SVG Renderer
    echo "Test 6: SVG Renderer\n";
    echo "-------------------\n";
    
    $renderer = new SvgRenderer($qr);
    $renderedSvg = $renderer->render();
    echo "✓ SVG renderer works successfully\n";
    echo "  Rendered SVG length: " . strlen($renderedSvg) . " characters\n";
    
    // Test different styles
    $rendererRects = new SvgRenderer($qr, SvgRenderer::STYLE_RECTANGLES);
    $rectSvg = $rendererRects->render();
    echo "✓ Rectangle style rendering works\n";
    
    $rendererDots = new SvgRenderer($qr, SvgRenderer::STYLE_DOTS);
    $dotSvg = $rendererDots->render();
    echo "✓ Dot style rendering works\n";
    
    // Save different styles
    file_put_contents('test_renderer_rects.svg', $rectSvg);
    file_put_contents('test_renderer_dots.svg', $dotSvg);
    echo "✓ Different styles saved\n\n";
    
    // Test 7: Complex Logo
    echo "Test 7: Complex Logo\n";
    echo "-------------------\n";
    
    $complexLogo = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
        <defs>
            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#6f42c1;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#e83e8c;stop-opacity:1" />
            </linearGradient>
        </defs>
        <circle cx="100" cy="100" r="80" fill="url(#grad1)"/>
        <text x="100" y="110" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold">LOGO</text>
    </svg>';
    
    $complexConfig = new LogoConfig($complexLogo, sizePercentage: 0.18);
    $complexQrWithLogo = new QrCodeLogo($qr, $complexConfig);
    $complexSvg = $complexQrWithLogo->toSvg();
    
    file_put_contents('test_complex_logo.svg', $complexSvg);
    echo "✓ Complex logo with gradient generated successfully\n";
    echo "  Saved to test_complex_logo.svg\n\n";
    
    // Test 8: Different Error Correction Levels
    echo "Test 8: Different Error Correction Levels\n";
    echo "-----------------------------------------\n";
    
    $eccLevels = [
        ['level' => Ecc::LOW, 'name' => 'LOW', 'maxSize' => 0.15],
        ['level' => Ecc::MEDIUM, 'name' => 'MEDIUM', 'maxSize' => 0.20],
        ['level' => Ecc::QUARTILE, 'name' => 'QUARTILE', 'maxSize' => 0.25],
        ['level' => Ecc::HIGH, 'name' => 'HIGH', 'maxSize' => 0.30],
    ];
    
    foreach ($eccLevels as $eccInfo) {
        $testQr = QrCode::encodeText("Test ECC " . $eccInfo['name'], $eccInfo['level']);
        $testConfig = new LogoConfig($logoSvg, sizePercentage: $eccInfo['maxSize']);
        $testQrWithLogo = new QrCodeLogo($testQr, $testConfig);
        $testSvg = $testQrWithLogo->toSvg();
        
        $filename = "test_ecc_" . strtolower($eccInfo['name']) . ".svg";
        file_put_contents($filename, $testSvg);
        
        echo "✓ {$eccInfo['name']} ECC with {$eccInfo['maxSize']}% logo - saved to $filename\n";
    }
    
    echo "\nAll tests completed successfully!\n";
    echo "Generated files:\n";
    echo "- test_logo_output.svg (basic logo)\n";
    echo "- test_renderer_rects.svg (rectangle style)\n";
    echo "- test_renderer_dots.svg (dot style)\n";
    echo "- test_complex_logo.svg (complex gradient logo)\n";
    echo "- test_ecc_*.svg (different error correction levels)\n";
    
} catch (Exception $e) {
    echo "✗ Error: {$e->getMessage()}\n";
    echo "  File: {$e->getFile()}\n";
    echo "  Line: {$e->getLine()}\n";
    if ($e instanceof LogoException) {
        echo "  Code: {$e->getCode()}\n";
    }
}
