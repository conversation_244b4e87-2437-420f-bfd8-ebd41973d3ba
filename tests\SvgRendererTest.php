<?php

declare(strict_types=1);

namespace QrCodeGen\Tests;

use PHPUnit\Framework\TestCase;
use QrCodeGen\QrCode;
use QrCodeGen\SvgRenderer;
use QrCodeGen\Ecc;

/**
 * Unit tests for SvgRenderer class.
 * 
 * @covers \QrCodeGen\SvgRenderer
 */
class SvgRendererTest extends TestCase
{
    private QrCode $qrCode;

    protected function setUp(): void
    {
        $this->qrCode = QrCode::encodeText("Test QR Code", Ecc::MEDIUM);
    }

    public function testConstructorWithDefaults(): void
    {
        $renderer = new SvgRenderer($this->qrCode);
        
        // Test that renderer is created without errors
        $this->assertInstanceOf(SvgRenderer::class, $renderer);
    }

    public function testConstructorWithCustomStyle(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_RECTANGLES);
        
        $this->assertInstanceOf(SvgRenderer::class, $renderer);
    }

    public function testConstructorWithOptions(): void
    {
        $options = [
            'responsive' => true,
            'title' => 'Test QR Code',
            'description' => 'A test QR code',
            'class' => 'qr-code',
            'id' => 'test-qr'
        ];
        
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH, $options);
        
        $this->assertInstanceOf(SvgRenderer::class, $renderer);
    }

    public function testRenderWithDefaultStyle(): void
    {
        $renderer = new SvgRenderer($this->qrCode);
        $svg = $renderer->render();
        
        $this->assertStringStartsWith('<?xml version="1.0"', $svg);
        $this->assertStringContainsString('<svg xmlns="http://www.w3.org/2000/svg"', $svg);
        $this->assertStringContainsString('</svg>', $svg);
        $this->assertStringContainsString('<path d="', $svg);
    }

    public function testRenderWithRectanglesStyle(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_RECTANGLES);
        $svg = $renderer->render();
        
        $this->assertStringContainsString('<rect x=', $svg);
        $this->assertStringNotContainsString('<path d="', $svg);
    }

    public function testRenderWithRoundedStyle(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_ROUNDED);
        $svg = $renderer->render();
        
        $this->assertStringContainsString('<rect x=', $svg);
        $this->assertStringContainsString('rx=', $svg);
        $this->assertStringContainsString('ry=', $svg);
    }

    public function testRenderWithDotsStyle(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_DOTS);
        $svg = $renderer->render();
        
        $this->assertStringContainsString('<circle cx=', $svg);
        $this->assertStringContainsString('cy=', $svg);
        $this->assertStringContainsString('r=', $svg);
    }

    public function testRenderWithCustomColors(): void
    {
        $renderer = new SvgRenderer($this->qrCode);
        $svg = $renderer->render(
            border: 4,
            lightColor: '#F0F0F0',
            darkColor: '#333333'
        );
        
        $this->assertStringContainsString('fill="#F0F0F0"', $svg);
        $this->assertStringContainsString('fill="#333333"', $svg);
    }

    public function testRenderWithCustomBorder(): void
    {
        $renderer = new SvgRenderer($this->qrCode);
        $svg = $renderer->render(border: 8);
        
        $expectedSize = $this->qrCode->size + 16; // border * 2
        $this->assertStringContainsString("viewBox=\"0 0 $expectedSize $expectedSize\"", $svg);
    }

    public function testRenderWithGradient(): void
    {
        $renderer = new SvgRenderer($this->qrCode);
        $svg = $renderer->renderWithGradient();
        
        $this->assertStringContainsString('<defs>', $svg);
        $this->assertStringContainsString('<linearGradient', $svg);
        $this->assertStringContainsString('url(#qr-gradient)', $svg);
    }

    public function testRenderWithGradientCustomColors(): void
    {
        $renderer = new SvgRenderer($this->qrCode);
        $svg = $renderer->renderWithGradient(
            border: 4,
            lightColor: '#FFFFFF',
            gradientStart: '#FF0000',
            gradientEnd: '#0000FF',
            gradientDirection: 'horizontal'
        );
        
        $this->assertStringContainsString('stop-color="#FF0000"', $svg);
        $this->assertStringContainsString('stop-color="#0000FF"', $svg);
        $this->assertStringContainsString('x1="0%" y1="0%" x2="100%" y2="0%"', $svg);
    }

    public function testRenderWithGradientVertical(): void
    {
        $renderer = new SvgRenderer($this->qrCode);
        $svg = $renderer->renderWithGradient(gradientDirection: 'vertical');
        
        $this->assertStringContainsString('x1="0%" y1="0%" x2="0%" y2="100%"', $svg);
    }

    public function testRenderWithGradientDiagonal(): void
    {
        $renderer = new SvgRenderer($this->qrCode);
        $svg = $renderer->renderWithGradient(gradientDirection: 'diagonal');
        
        $this->assertStringContainsString('x1="0%" y1="0%" x2="100%" y2="100%"', $svg);
    }

    public function testRenderWithCustomShapesRounded(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_ROUNDED);
        $svg = $renderer->renderWithCustomShapes(cornerRadius: 0.3);
        
        $this->assertStringContainsString('rx="0.3"', $svg);
        $this->assertStringContainsString('ry="0.3"', $svg);
    }

    public function testRenderWithCustomShapesDots(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_DOTS);
        $svg = $renderer->renderWithCustomShapes();
        
        $this->assertStringContainsString('<circle', $svg);
        $this->assertStringContainsString('r="0.4"', $svg);
    }

    public function testResponsiveOption(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH, ['responsive' => true]);
        $svg = $renderer->render();
        
        // Should not contain width/height attributes when responsive
        $this->assertStringNotContainsString('width="', $svg);
        $this->assertStringNotContainsString('height="', $svg);
        $this->assertStringContainsString('viewBox=', $svg);
    }

    public function testNonResponsiveOption(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH, ['responsive' => false]);
        $svg = $renderer->render();
        
        // Should contain width/height attributes when not responsive
        $this->assertStringContainsString('width="', $svg);
        $this->assertStringContainsString('height="', $svg);
    }

    public function testTitleOption(): void
    {
        $title = 'Test QR Code Title';
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH, ['title' => $title]);
        $svg = $renderer->render();
        
        $this->assertStringContainsString("<title>$title</title>", $svg);
    }

    public function testDescriptionOption(): void
    {
        $description = 'This is a test QR code description';
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH, ['description' => $description]);
        $svg = $renderer->render();
        
        $this->assertStringContainsString("<desc>$description</desc>", $svg);
    }

    public function testClassOption(): void
    {
        $className = 'my-qr-code';
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH, ['class' => $className]);
        $svg = $renderer->render();
        
        $this->assertStringContainsString("class=\"$className\"", $svg);
    }

    public function testIdOption(): void
    {
        $id = 'qr-code-123';
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH, ['id' => $id]);
        $svg = $renderer->render();
        
        $this->assertStringContainsString("id=\"$id\"", $svg);
    }

    public function testAllOptionsTogther(): void
    {
        $options = [
            'responsive' => true,
            'title' => 'Complete Test',
            'description' => 'Testing all options',
            'class' => 'test-class',
            'id' => 'test-id'
        ];
        
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH, $options);
        $svg = $renderer->render();
        
        $this->assertStringContainsString('<title>Complete Test</title>', $svg);
        $this->assertStringContainsString('<desc>Testing all options</desc>', $svg);
        $this->assertStringContainsString('class="test-class"', $svg);
        $this->assertStringContainsString('id="test-id"', $svg);
        $this->assertStringNotContainsString('width="', $svg);
        $this->assertStringNotContainsString('height="', $svg);
    }

    public function testSvgStructureValidity(): void
    {
        $renderer = new SvgRenderer($this->qrCode);
        $svg = $renderer->render();
        
        // Test basic XML structure
        $this->assertStringStartsWith('<?xml version="1.0" encoding="UTF-8"?>', $svg);
        $this->assertStringContainsString('<!DOCTYPE svg', $svg);
        $this->assertStringContainsString('<svg xmlns="http://www.w3.org/2000/svg"', $svg);
        $this->assertStringEndsWith("</svg>\n", $svg);
        
        // Test SVG attributes
        $this->assertStringContainsString('version="1.1"', $svg);
        $this->assertStringContainsString('viewBox=', $svg);
        $this->assertStringContainsString('stroke="none"', $svg);
    }

    public function testModuleCountConsistency(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_RECTANGLES);
        $svg = $renderer->render();
        
        // Count rectangles in SVG
        $rectCount = substr_count($svg, '<rect x=');
        
        // Count actual dark modules in QR code
        $darkModules = 0;
        for ($y = 0; $y < $this->qrCode->size; $y++) {
            for ($x = 0; $x < $this->qrCode->size; $x++) {
                if ($this->qrCode->getModule($x, $y)) {
                    $darkModules++;
                }
            }
        }
        
        // Should have one rectangle per dark module plus background
        $this->assertEquals($darkModules + 1, $rectCount); // +1 for background
    }

    public function testPathStyleModuleCount(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH);
        $svg = $renderer->render();
        
        // Extract path data
        preg_match('/<path d="([^"]+)"/', $svg, $matches);
        $this->assertCount(2, $matches);
        
        $pathData = $matches[1];
        $moveCommands = substr_count($pathData, 'M');
        
        // Count actual dark modules
        $darkModules = 0;
        for ($y = 0; $y < $this->qrCode->size; $y++) {
            for ($x = 0; $x < $this->qrCode->size; $x++) {
                if ($this->qrCode->getModule($x, $y)) {
                    $darkModules++;
                }
            }
        }
        
        $this->assertEquals($darkModules, $moveCommands);
    }

    public function testDifferentQrCodeSizes(): void
    {
        $smallQr = QrCode::encodeText("Hi", Ecc::LOW);
        $largeQr = QrCode::encodeText("This is a much longer text that will create a larger QR code with more modules", Ecc::HIGH);
        
        $smallRenderer = new SvgRenderer($smallQr);
        $largeRenderer = new SvgRenderer($largeQr);
        
        $smallSvg = $smallRenderer->render();
        $largeSvg = $largeRenderer->render();
        
        // Both should be valid SVG
        $this->assertStringContainsString('<svg', $smallSvg);
        $this->assertStringContainsString('<svg', $largeSvg);
        
        // Large QR should have larger viewBox
        preg_match('/viewBox="0 0 (\d+) (\d+)"/', $smallSvg, $smallMatches);
        preg_match('/viewBox="0 0 (\d+) (\d+)"/', $largeSvg, $largeMatches);
        
        $this->assertGreaterThan((int)$smallMatches[1], (int)$largeMatches[1]);
    }

    public function testCornerRadiusBoundaries(): void
    {
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_ROUNDED);
        
        // Test minimum radius
        $svg = $renderer->renderWithCustomShapes(cornerRadius: 0.0);
        $this->assertStringContainsString('rx="0"', $svg);
        
        // Test maximum radius
        $svg = $renderer->renderWithCustomShapes(cornerRadius: 0.5);
        $this->assertStringContainsString('rx="0.5"', $svg);
        
        // Test value above maximum (should be clamped)
        $svg = $renderer->renderWithCustomShapes(cornerRadius: 0.8);
        $this->assertStringContainsString('rx="0.5"', $svg);
    }

    public function testHtmlEscapingInOptions(): void
    {
        $options = [
            'title' => 'Title with <script>alert("xss")</script>',
            'description' => 'Description with & special chars < >'
        ];
        
        $renderer = new SvgRenderer($this->qrCode, SvgRenderer::STYLE_PATH, $options);
        $svg = $renderer->render();
        
        // Should escape HTML entities
        $this->assertStringContainsString('&lt;script&gt;', $svg);
        $this->assertStringContainsString('&amp;', $svg);
        $this->assertStringContainsString('&lt;', $svg);
        $this->assertStringContainsString('&gt;', $svg);
    }
}
