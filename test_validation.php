<?php

declare(strict_types=1);

// Manual includes
require_once 'src/functions.php';
require_once 'src/Ecc.php';
require_once 'src/Mode.php';
require_once 'src/QrSegment.php';
require_once 'src/QrCode.php';
require_once 'src/Exceptions/LogoException.php';
require_once 'src/LogoConfig.php';
require_once 'src/QrCodeLogo.php';

use QrCodeGen\QrCode;
use QrCodeGen\QrCodeLogo;
use QrCodeGen\LogoConfig;
use QrCodeGen\Ecc;
use QrCodeGen\Exceptions\LogoException;

echo "Validation Test\n";
echo "===============\n\n";

$logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="40" fill="#007bff"/>
</svg>';

// Test ECC levels and their limits
$eccLevels = [
    ['level' => Ecc::LOW, 'name' => 'LOW', 'maxSize' => 0.15],
    ['level' => Ecc::MEDIUM, 'name' => 'MEDIUM', 'maxSize' => 0.20],
    ['level' => Ecc::QUARTILE, 'name' => 'QUARTILE', 'maxSize' => 0.25],
    ['level' => Ecc::HIGH, 'name' => 'HIGH', 'maxSize' => 0.30],
];

foreach ($eccLevels as $eccInfo) {
    echo "Testing {$eccInfo['name']} ECC (max {$eccInfo['maxSize']}):\n";

    // Use encodeSegments with boostEcl=false to prevent automatic ECC boosting
    $segs = \QrCodeGen\QrSegment::makeSegments("Test data for " . $eccInfo['name']);
    $qr = QrCode::encodeSegments($segs, $eccInfo['level'], 1, 40, -1, false);
    echo "  QR Code ECC level: {$qr->errorCorrectionLevel->name}\n";
    
    // Test with valid size
    try {
        $validConfig = new LogoConfig($logoSvg, sizePercentage: $eccInfo['maxSize']);
        $validQrWithLogo = new QrCodeLogo($qr, $validConfig);
        echo "  ✓ Valid size {$eccInfo['maxSize']} accepted\n";
    } catch (LogoException $e) {
        echo "  ✗ Valid size {$eccInfo['maxSize']} rejected: {$e->getMessage()}\n";
    }
    
    // Test with invalid size (slightly over limit)
    $invalidSize = $eccInfo['maxSize'] + 0.05;
    try {
        $invalidConfig = new LogoConfig($logoSvg, sizePercentage: $invalidSize);
        $invalidQrWithLogo = new QrCodeLogo($qr, $invalidConfig);
        echo "  ✗ Invalid size $invalidSize should have been rejected but wasn't\n";
    } catch (LogoException $e) {
        echo "  ✓ Invalid size $invalidSize correctly rejected: {$e->getMessage()}\n";
    }
    
    echo "\n";
}

// Specific test for LOW ECC with 0.25 size
echo "Specific test: LOW ECC with 0.25 size\n";
echo "-------------------------------------\n";

try {
    $segs = \QrCodeGen\QrSegment::makeSegments("Test");
    $lowQr = QrCode::encodeSegments($segs, Ecc::LOW, 1, 40, -1, false);
    echo "QR Code created with ECC: {$lowQr->errorCorrectionLevel->name}\n";
    
    $largeConfig = new LogoConfig($logoSvg, sizePercentage: 0.25);
    echo "Logo config created with size: 0.25\n";
    
    echo "Max allowed for LOW: " . LogoConfig::getMaxLogoSize(Ecc::LOW) . "\n";
    
    $largeQrWithLogo = new QrCodeLogo($lowQr, $largeConfig);
    echo "✗ QrCodeLogo created - should have failed!\n";
    
} catch (LogoException $e) {
    echo "✓ Correctly caught exception: {$e->getMessage()}\n";
    echo "  Exception code: {$e->getCode()}\n";
} catch (Exception $e) {
    echo "✗ Unexpected exception: {$e->getMessage()}\n";
}
