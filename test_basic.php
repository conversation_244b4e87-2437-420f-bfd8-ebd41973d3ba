<?php

declare(strict_types=1);

// Simple autoloader for testing without composer
spl_autoload_register(function ($class) {
    $prefix = 'QrCodeGen\\';
    $base_dir = __DIR__ . '/src/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

// Include functions
require_once __DIR__ . '/src/functions.php';

use QrCodeGen\Ecc;
use QrCodeGen\Mode;
use QrCodeGen\QrSegment;

echo "Testing QR Code Generator PHP Port...\n\n";

// Test Ecc enum
echo "Testing Ecc enum:\n";
echo "LOW ordinal: " . Ecc::LOW->getOrdinal() . " (expected: 0)\n";
echo "MEDIUM ordinal: " . Ecc::MEDIUM->getOrdinal() . " (expected: 1)\n";
echo "QUARTILE ordinal: " . Ecc::QUARTILE->getOrdinal() . " (expected: 2)\n";
echo "HIGH ordinal: " . Ecc::HIGH->getOrdinal() . " (expected: 3)\n";

echo "LOW format bits: " . Ecc::LOW->getFormatBits() . " (expected: 1)\n";
echo "MEDIUM format bits: " . Ecc::MEDIUM->getFormatBits() . " (expected: 0)\n";
echo "QUARTILE format bits: " . Ecc::QUARTILE->getFormatBits() . " (expected: 3)\n";
echo "HIGH format bits: " . Ecc::HIGH->getFormatBits() . " (expected: 2)\n\n";

// Test Mode enum
echo "Testing Mode enum:\n";
echo "NUMERIC mode bits: " . Mode::NUMERIC->getModeBits() . " (expected: 1)\n";
echo "ALPHANUMERIC mode bits: " . Mode::ALPHANUMERIC->getModeBits() . " (expected: 2)\n";
echo "BYTE mode bits: " . Mode::BYTE->getModeBits() . " (expected: 4)\n";
echo "KANJI mode bits: " . Mode::KANJI->getModeBits() . " (expected: 8)\n";
echo "ECI mode bits: " . Mode::ECI->getModeBits() . " (expected: 7)\n\n";

// Test Mode character count bits
echo "Testing Mode character count bits:\n";
echo "NUMERIC v1: " . Mode::NUMERIC->getNumCharCountBits(1) . " (expected: 10)\n";
echo "NUMERIC v10: " . Mode::NUMERIC->getNumCharCountBits(10) . " (expected: 12)\n";
echo "NUMERIC v27: " . Mode::NUMERIC->getNumCharCountBits(27) . " (expected: 14)\n\n";

// Test QrSegment validation
echo "Testing QrSegment validation:\n";
echo "isNumeric('123456'): " . (QrSegment::isNumeric('123456') ? 'true' : 'false') . " (expected: true)\n";
echo "isNumeric('123a'): " . (QrSegment::isNumeric('123a') ? 'true' : 'false') . " (expected: false)\n";
echo "isAlphanumeric('HELLO123'): " . (QrSegment::isAlphanumeric('HELLO123') ? 'true' : 'false') . " (expected: true)\n";
echo "isAlphanumeric('hello'): " . (QrSegment::isAlphanumeric('hello') ? 'true' : 'false') . " (expected: false)\n\n";

// Test QrSegment creation
echo "Testing QrSegment creation:\n";
$numSeg = QrSegment::makeNumeric('123456');
echo "Numeric segment mode: " . $numSeg->mode->name . " (expected: NUMERIC)\n";
echo "Numeric segment numChars: " . $numSeg->numChars . " (expected: 6)\n";

$alphaSeg = QrSegment::makeAlphanumeric('HELLO');
echo "Alphanumeric segment mode: " . $alphaSeg->mode->name . " (expected: ALPHANUMERIC)\n";
echo "Alphanumeric segment numChars: " . $alphaSeg->numChars . " (expected: 5)\n";

$byteSeg = QrSegment::makeBytes([72, 101, 108, 108, 111]); // "Hello"
echo "Byte segment mode: " . $byteSeg->mode->name . " (expected: BYTE)\n";
echo "Byte segment numChars: " . $byteSeg->numChars . " (expected: 5)\n\n";

// Test makeSegments automatic mode selection
echo "Testing makeSegments automatic mode selection:\n";
$numSegs = QrSegment::makeSegments('123456');
echo "Numeric string segments count: " . count($numSegs) . " (expected: 1)\n";
echo "Numeric string segment mode: " . $numSegs[0]->mode->name . " (expected: NUMERIC)\n";

$alphaSegs = QrSegment::makeSegments('HELLO123');
echo "Alphanumeric string segments count: " . count($alphaSegs) . " (expected: 1)\n";
echo "Alphanumeric string segment mode: " . $alphaSegs[0]->mode->name . " (expected: ALPHANUMERIC)\n";

$byteSegs = QrSegment::makeSegments('Hello, world!');
echo "Mixed string segments count: " . count($byteSegs) . " (expected: 1)\n";
echo "Mixed string segment mode: " . $byteSegs[0]->mode->name . " (expected: BYTE)\n\n";

// Test utility functions
echo "Testing utility functions:\n";
$bb = [];
QrCodeGen\appendBits(5, 3, $bb); // 5 = 101 in binary
echo "appendBits(5, 3): [" . implode(', ', $bb) . "] (expected: [1, 0, 1])\n";

echo "getBit(5, 0): " . (QrCodeGen\getBit(5, 0) ? 'true' : 'false') . " (expected: true)\n";
echo "getBit(5, 1): " . (QrCodeGen\getBit(5, 1) ? 'true' : 'false') . " (expected: false)\n";
echo "getBit(5, 2): " . (QrCodeGen\getBit(5, 2) ? 'true' : 'false') . " (expected: true)\n\n";

echo "All basic tests completed successfully!\n";
