<?php

declare(strict_types=1);

require_once __DIR__ . '/../src/functions.php';
require_once __DIR__ . '/../src/Ecc.php';
require_once __DIR__ . '/../src/Mode.php';
require_once __DIR__ . '/../src/QrSegment.php';
require_once __DIR__ . '/../src/QrCode.php';

use QrCodeGen\QrCode;
use QrCodeGen\QrSegment;
use QrCodeGen\Ecc;

echo "QR Code Generator - Basic Usage Examples\n";
echo "========================================\n\n";

// Example 1: Simple text encoding
echo "Example 1: Simple text encoding\n";
echo "-------------------------------\n";
$qr1 = QrCode::encodeText("Hello, world!", Ecc::MEDIUM);
echo "Text: 'Hello, world!'\n";
echo "Version: {$qr1->version}\n";
echo "Size: {$qr1->size}×{$qr1->size}\n";
echo "Error correction: {$qr1->errorCorrectionLevel->name}\n";
echo "Mask: {$qr1->mask}\n\n";

// Example 2: URL encoding
echo "Example 2: URL encoding\n";
echo "-----------------------\n";
$url = "https://www.example.com/page?param=value";
$qr2 = QrCode::encodeText($url, Ecc::LOW);
echo "URL: $url\n";
echo "Version: {$qr2->version}\n";
echo "Size: {$qr2->size}×{$qr2->size}\n";
echo "Error correction: {$qr2->errorCorrectionLevel->name}\n\n";

// Example 3: Numeric data (automatically optimized)
echo "Example 3: Numeric data encoding\n";
echo "--------------------------------\n";
$numbers = "1234567890123456789";
$qr3 = QrCode::encodeText($numbers, Ecc::HIGH);
echo "Numbers: $numbers\n";
echo "Version: {$qr3->version}\n";
echo "Size: {$qr3->size}×{$qr3->size}\n";
echo "Error correction: {$qr3->errorCorrectionLevel->name}\n";
echo "Note: Automatically uses numeric mode for efficiency\n\n";

// Example 4: Alphanumeric data (automatically optimized)
echo "Example 4: Alphanumeric data encoding\n";
echo "------------------------------------\n";
$alphanumeric = "PRODUCT-CODE-12345";
$qr4 = QrCode::encodeText($alphanumeric, Ecc::QUARTILE);
echo "Text: $alphanumeric\n";
echo "Version: {$qr4->version}\n";
echo "Size: {$qr4->size}×{$qr4->size}\n";
echo "Error correction: {$qr4->errorCorrectionLevel->name}\n";
echo "Note: Automatically uses alphanumeric mode for efficiency\n\n";

// Example 5: Binary data encoding
echo "Example 5: Binary data encoding\n";
echo "-------------------------------\n";
$binaryData = [0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x20, 0x57, 0x6F, 0x72, 0x6C, 0x64, 0x21]; // "Hello World!"
$qr5 = QrCode::encodeBinary($binaryData, Ecc::MEDIUM);
echo "Binary data: [" . implode(', ', array_map(fn($b) => sprintf('0x%02X', $b), $binaryData)) . "]\n";
echo "As text: '" . implode('', array_map('chr', $binaryData)) . "'\n";
echo "Version: {$qr5->version}\n";
echo "Size: {$qr5->size}×{$qr5->size}\n";
echo "Error correction: {$qr5->errorCorrectionLevel->name}\n\n";

// Example 6: Comparing error correction levels
echo "Example 6: Error correction level comparison\n";
echo "-------------------------------------------\n";
$testText = "QR Code with different error correction levels";
echo "Text: '$testText'\n\n";

foreach ([Ecc::LOW, Ecc::MEDIUM, Ecc::QUARTILE, Ecc::HIGH] as $ecc) {
    $qr = QrCode::encodeText($testText, $ecc);
    $capacity = calculateDataCapacity($qr->version, $ecc);
    echo sprintf("%-9s: Version %2d, Size %3d×%3d, Data capacity: %4d bytes\n", 
        $ecc->name, $qr->version, $qr->size, $qr->size, $capacity);
}
echo "\n";

// Example 7: Manual segment creation for optimal encoding
echo "Example 7: Manual segment optimization\n";
echo "-------------------------------------\n";
$segments = [
    QrSegment::makeAlphanumeric("PRODUCT-"),
    QrSegment::makeNumeric("12345"),
    QrSegment::makeBytes([0x20]), // Space character
    QrSegment::makeAlphanumeric("BATCH-"),
    QrSegment::makeNumeric("67890")
];

$qr7 = QrCode::encodeSegments($segments, Ecc::MEDIUM);
echo "Mixed segments: PRODUCT-12345 BATCH-67890\n";
echo "Segments used:\n";
echo "  - Alphanumeric: 'PRODUCT-'\n";
echo "  - Numeric: '12345'\n";
echo "  - Byte: [space]\n";
echo "  - Alphanumeric: 'BATCH-'\n";
echo "  - Numeric: '67890'\n";
echo "Version: {$qr7->version}\n";
echo "Size: {$qr7->size}×{$qr7->size}\n";
echo "Error correction: {$qr7->errorCorrectionLevel->name}\n\n";

// Example 8: Version constraints
echo "Example 8: Version constraints\n";
echo "-----------------------------\n";
$constrainedQr = QrCode::encodeSegments(
    QrSegment::makeSegments("Test with version constraints"),
    Ecc::LOW,
    5,    // Minimum version
    10,   // Maximum version
    -1,   // Auto mask
    true  // Boost error correction
);
echo "Text: 'Test with version constraints'\n";
echo "Requested version range: 5-10\n";
echo "Actual version: {$constrainedQr->version}\n";
echo "Size: {$constrainedQr->size}×{$constrainedQr->size}\n";
echo "Error correction: {$constrainedQr->errorCorrectionLevel->name} (may be boosted)\n\n";

// Helper function to calculate data capacity
function calculateDataCapacity(int $version, Ecc $ecc): int
{
    // This is a simplified calculation - the actual implementation is in QrCode class
    $totalModules = ($version * 4 + 17) ** 2;
    $functionModules = estimateFunctionModules($version);
    $dataModules = $totalModules - $functionModules;
    $totalCodewords = intval($dataModules / 8);
    
    // Rough estimation of ECC overhead
    $eccOverhead = match($ecc) {
        Ecc::LOW => 0.07,
        Ecc::MEDIUM => 0.15,
        Ecc::QUARTILE => 0.25,
        Ecc::HIGH => 0.30,
    };
    
    return intval($totalCodewords * (1 - $eccOverhead));
}

function estimateFunctionModules(int $version): int
{
    // Rough estimation of function modules (finder patterns, timing patterns, etc.)
    $base = 202; // Base function modules for version 1
    $alignmentPatterns = max(0, intval($version / 7) + 2 - 3) ** 2 * 25;
    $versionInfo = $version >= 7 ? 36 : 0;
    return $base + $alignmentPatterns + $versionInfo;
}

echo "All examples completed successfully!\n";
echo "\nNote: To see the actual QR code patterns, use the display functions\n";
echo "from the other example files or implement your own output format.\n";
