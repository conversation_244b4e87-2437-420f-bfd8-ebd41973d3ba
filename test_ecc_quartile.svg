<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 33 33" stroke="none">
	<rect width="100%" height="100%" fill="#FFFFFF"/>
	<path d="M4,4 h1v1h-1zM5,4 h1v1h-1zM6,4 h1v1h-1zM7,4 h1v1h-1zM8,4 h1v1h-1zM9,4 h1v1h-1zM10,4 h1v1h-1zM15,4 h1v1h-1zM18,4 h1v1h-1zM22,4 h1v1h-1zM23,4 h1v1h-1zM24,4 h1v1h-1zM25,4 h1v1h-1zM26,4 h1v1h-1zM27,4 h1v1h-1zM28,4 h1v1h-1zM4,5 h1v1h-1zM10,5 h1v1h-1zM12,5 h1v1h-1zM14,5 h1v1h-1zM15,5 h1v1h-1zM16,5 h1v1h-1zM17,5 h1v1h-1zM22,5 h1v1h-1zM28,5 h1v1h-1zM4,6 h1v1h-1zM6,6 h1v1h-1zM7,6 h1v1h-1zM8,6 h1v1h-1zM10,6 h1v1h-1zM15,6 h1v1h-1zM16,6 h1v1h-1zM19,6 h1v1h-1zM22,6 h1v1h-1zM24,6 h1v1h-1zM25,6 h1v1h-1zM26,6 h1v1h-1zM28,6 h1v1h-1zM4,7 h1v1h-1zM6,7 h1v1h-1zM7,7 h1v1h-1zM8,7 h1v1h-1zM10,7 h1v1h-1zM14,7 h1v1h-1zM15,7 h1v1h-1zM17,7 h1v1h-1zM22,7 h1v1h-1zM24,7 h1v1h-1zM25,7 h1v1h-1zM26,7 h1v1h-1zM28,7 h1v1h-1zM4,8 h1v1h-1zM6,8 h1v1h-1zM7,8 h1v1h-1zM8,8 h1v1h-1zM10,8 h1v1h-1zM17,8 h1v1h-1zM20,8 h1v1h-1zM22,8 h1v1h-1zM24,8 h1v1h-1zM25,8 h1v1h-1zM26,8 h1v1h-1zM28,8 h1v1h-1zM4,9 h1v1h-1zM10,9 h1v1h-1zM15,9 h1v1h-1zM18,9 h1v1h-1zM19,9 h1v1h-1zM20,9 h1v1h-1zM22,9 h1v1h-1zM28,9 h1v1h-1zM4,10 h1v1h-1zM5,10 h1v1h-1zM6,10 h1v1h-1zM7,10 h1v1h-1zM8,10 h1v1h-1zM9,10 h1v1h-1zM10,10 h1v1h-1zM12,10 h1v1h-1zM14,10 h1v1h-1zM16,10 h1v1h-1zM18,10 h1v1h-1zM20,10 h1v1h-1zM22,10 h1v1h-1zM23,10 h1v1h-1zM24,10 h1v1h-1zM25,10 h1v1h-1zM26,10 h1v1h-1zM27,10 h1v1h-1zM28,10 h1v1h-1zM12,11 h1v1h-1zM13,11 h1v1h-1zM15,11 h1v1h-1zM19,11 h1v1h-1zM20,11 h1v1h-1zM5,12 h1v1h-1zM6,12 h1v1h-1zM8,12 h1v1h-1zM10,12 h1v1h-1zM11,12 h1v1h-1zM22,12 h1v1h-1zM24,12 h1v1h-1zM25,12 h1v1h-1zM26,12 h1v1h-1zM27,12 h1v1h-1zM28,12 h1v1h-1zM5,13 h1v1h-1zM7,13 h1v1h-1zM11,13 h1v1h-1zM23,13 h1v1h-1zM28,13 h1v1h-1zM4,14 h1v1h-1zM6,14 h1v1h-1zM10,14 h1v1h-1zM11,14 h1v1h-1zM22,14 h1v1h-1zM23,14 h1v1h-1zM24,14 h1v1h-1zM25,14 h1v1h-1zM27,14 h1v1h-1zM28,14 h1v1h-1zM5,15 h1v1h-1zM7,15 h1v1h-1zM9,15 h1v1h-1zM11,15 h1v1h-1zM4,16 h1v1h-1zM6,16 h1v1h-1zM8,16 h1v1h-1zM10,16 h1v1h-1zM11,16 h1v1h-1zM22,16 h1v1h-1zM24,16 h1v1h-1zM26,16 h1v1h-1zM27,16 h1v1h-1zM28,16 h1v1h-1zM5,17 h1v1h-1zM7,17 h1v1h-1zM8,17 h1v1h-1zM9,17 h1v1h-1zM23,17 h1v1h-1zM25,17 h1v1h-1zM27,17 h1v1h-1zM28,17 h1v1h-1zM4,18 h1v1h-1zM6,18 h1v1h-1zM8,18 h1v1h-1zM9,18 h1v1h-1zM10,18 h1v1h-1zM22,18 h1v1h-1zM27,18 h1v1h-1zM28,18 h1v1h-1zM5,19 h1v1h-1zM7,19 h1v1h-1zM9,19 h1v1h-1zM23,19 h1v1h-1zM25,19 h1v1h-1zM27,19 h1v1h-1zM4,20 h1v1h-1zM6,20 h1v1h-1zM8,20 h1v1h-1zM10,20 h1v1h-1zM11,20 h1v1h-1zM22,20 h1v1h-1zM23,20 h1v1h-1zM24,20 h1v1h-1zM26,20 h1v1h-1zM27,20 h1v1h-1zM28,20 h1v1h-1zM24,21 h1v1h-1zM4,22 h1v1h-1zM5,22 h1v1h-1zM6,22 h1v1h-1zM7,22 h1v1h-1zM8,22 h1v1h-1zM9,22 h1v1h-1zM10,22 h1v1h-1zM12,22 h1v1h-1zM14,22 h1v1h-1zM16,22 h1v1h-1zM20,22 h1v1h-1zM22,22 h1v1h-1zM24,22 h1v1h-1zM26,22 h1v1h-1zM27,22 h1v1h-1zM28,22 h1v1h-1zM4,23 h1v1h-1zM10,23 h1v1h-1zM13,23 h1v1h-1zM14,23 h1v1h-1zM17,23 h1v1h-1zM18,23 h1v1h-1zM20,23 h1v1h-1zM24,23 h1v1h-1zM4,24 h1v1h-1zM6,24 h1v1h-1zM7,24 h1v1h-1zM8,24 h1v1h-1zM10,24 h1v1h-1zM12,24 h1v1h-1zM14,24 h1v1h-1zM16,24 h1v1h-1zM20,24 h1v1h-1zM21,24 h1v1h-1zM22,24 h1v1h-1zM23,24 h1v1h-1zM24,24 h1v1h-1zM25,24 h1v1h-1zM26,24 h1v1h-1zM28,24 h1v1h-1zM4,25 h1v1h-1zM6,25 h1v1h-1zM7,25 h1v1h-1zM8,25 h1v1h-1zM10,25 h1v1h-1zM14,25 h1v1h-1zM18,25 h1v1h-1zM19,25 h1v1h-1zM24,25 h1v1h-1zM4,26 h1v1h-1zM6,26 h1v1h-1zM7,26 h1v1h-1zM8,26 h1v1h-1zM10,26 h1v1h-1zM12,26 h1v1h-1zM15,26 h1v1h-1zM17,26 h1v1h-1zM19,26 h1v1h-1zM20,26 h1v1h-1zM21,26 h1v1h-1zM22,26 h1v1h-1zM24,26 h1v1h-1zM26,26 h1v1h-1zM28,26 h1v1h-1zM4,27 h1v1h-1zM10,27 h1v1h-1zM12,27 h1v1h-1zM13,27 h1v1h-1zM14,27 h1v1h-1zM18,27 h1v1h-1zM21,27 h1v1h-1zM23,27 h1v1h-1zM25,27 h1v1h-1zM27,27 h1v1h-1zM4,28 h1v1h-1zM5,28 h1v1h-1zM6,28 h1v1h-1zM7,28 h1v1h-1zM8,28 h1v1h-1zM9,28 h1v1h-1zM10,28 h1v1h-1zM15,28 h1v1h-1zM16,28 h1v1h-1zM18,28 h1v1h-1zM19,28 h1v1h-1zM20,28 h1v1h-1zM23,28 h1v1h-1zM24,28 h1v1h-1zM25,28 h1v1h-1zM27,28 h1v1h-1zM28,28 h1v1h-1z" fill="#000000"/>
	<rect x="11.375" y="11.375" width="10.25" height="10.25" fill="#FFFFFF" rx="2" ry="2"/>
	<g transform="translate(13.375,13.375)">
		<svg x="0" y="0" width="6.25" height="6.25" viewBox="0 0 100 100">
			<circle cx="50" cy="50" r="40" fill="#007bff"/>
        <text x="50" y="55" text-anchor="middle" fill="white" font-family="Arial" font-size="20" font-weight="bold">QR</text>
		</svg>
	</g>
</svg>
