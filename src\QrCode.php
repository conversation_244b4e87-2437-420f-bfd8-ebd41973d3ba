<?php

declare(strict_types=1);

/*
 * QR Code generator library (PHP)
 * 
 * Copyright (c) Project Nayuki. (MIT License)
 * https://www.nayuki.io/page/qr-code-generator-library
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * - The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 * - The Software is provided "as is", without warranty of any kind, express or
 *   implied, including but not limited to the warranties of merchantability,
 *   fitness for a particular purpose and noninfringement. In no event shall the
 *   authors or copyright holders be liable for any claim, damages or other
 *   liability, whether in an action of contract, tort or otherwise, arising from,
 *   out of or in connection with the Software or the use or other dealings in the
 *   Software.
 */

namespace QrCodeGen;

use function QrCodeGen\appendBits;
use function QrCodeGen\getBit;
use function QrCodeGen\assertCondition;

/**
 * A QR Code symbol, which is a type of two-dimension barcode.
 * 
 * Invented by Denso Wave and described in the ISO/IEC 18004 standard.
 * Instances of this class represent an immutable square grid of dark and light cells.
 * The class provides static factory functions to create a QR Code from text or binary data.
 * The class covers the QR Code Model 2 specification, supporting all versions (sizes)
 * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.
 * 
 * Ways to create a QR Code object:
 * - High level: Take the payload data and call QrCode::encodeText() or QrCode::encodeBinary().
 * - Mid level: Custom-make the list of segments and call QrCode::encodeSegments().
 * - Low level: Custom-make the array of data codeword bytes (including
 *   segment headers and final padding, excluding error correction codewords),
 *   supply the appropriate version number, and call the QrCode() constructor.
 * (Note that all ways require supplying the desired error correction level.)
 */
class QrCode
{
    /**
     * The minimum version number supported in the QR Code Model 2 standard.
     */
    public const MIN_VERSION = 1;
    
    /**
     * The maximum version number supported in the QR Code Model 2 standard.
     */
    public const MAX_VERSION = 40;

    /**
     * For use in getPenaltyScore(), when evaluating which mask is best.
     */
    private const PENALTY_N1 = 3;
    private const PENALTY_N2 = 3;
    private const PENALTY_N3 = 40;
    private const PENALTY_N4 = 10;

    /**
     * Error correction codewords per block for each error correction level and version.
     *
     * @var array<array<int>>
     */
    private const ECC_CODEWORDS_PER_BLOCK = [
        // Version: (note that index 0 is for padding, and is set to an illegal value)
        //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level
        [-1,  7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],  // Low
        [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],  // Medium
        [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],  // Quartile
        [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],  // High
    ];

    /**
     * Number of error correction blocks for each error correction level and version.
     *
     * @var array<array<int>>
     */
    private const NUM_ERROR_CORRECTION_BLOCKS = [
        // Version: (note that index 0 is for padding, and is set to an illegal value)
        //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level
        [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4,  4,  4,  4,  4,  6,  6,  6,  6,  7,  8,  8,  9,  9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],  // Low
        [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5,  5,  8,  9,  9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],  // Medium
        [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8,  8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],  // Quartile
        [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81],  // High
    ];

    /**
     * The width and height of this QR Code, measured in modules, between 21 and 177 (inclusive).
     * This is equal to version * 4 + 17.
     */
    public readonly int $size;

    /**
     * The index of the mask pattern used in this QR Code, which is between 0 and 7 (inclusive).
     */
    public readonly int $mask;

    /**
     * The modules of this QR Code (false = light, true = dark).
     * Immutable after constructor finishes. Accessed through getModule().
     * 
     * @var array<array<bool>>
     */
    private readonly array $modules;

    /**
     * Creates a new QR Code with the given version number, error correction level, 
     * data codeword bytes, and mask number.
     * 
     * @param int $version The version number of this QR Code (1-40)
     * @param Ecc $errorCorrectionLevel The error correction level used
     * @param array<int> $dataCodewords The data codeword bytes
     * @param int $msk The mask number (-1 for automatic, 0-7 for manual)
     */
    public function __construct(
        public readonly int $version,
        public readonly Ecc $errorCorrectionLevel,
        array $dataCodewords,
        int $msk
    ) {
        // Check scalar arguments
        if ($version < self::MIN_VERSION || $version > self::MAX_VERSION) {
            throw new \InvalidArgumentException("Version value out of range");
        }
        if ($msk < -1 || $msk > 7) {
            throw new \InvalidArgumentException("Mask value out of range");
        }
        
        $this->size = $version * 4 + 17;
        
        // Initialize both grids to be size*size arrays of Boolean false
        $modules = [];
        $isFunction = [];
        for ($i = 0; $i < $this->size; $i++) {
            $modules[$i] = array_fill(0, $this->size, false);
            $isFunction[$i] = array_fill(0, $this->size, false);
        }
        
        // Compute ECC, draw modules
        $this->drawFunctionPatterns($modules, $isFunction);
        $allCodewords = $this->addEccAndInterleave($dataCodewords);
        $this->drawCodewords($allCodewords, $modules, $isFunction);
        
        // Do masking
        if ($msk === -1) {
            $minPenalty = 1000000000;
            for ($i = 0; $i < 8; $i++) {
                $this->applyMask($i, $modules, $isFunction);
                $this->drawFormatBits($i, $modules);
                $penalty = $this->getPenaltyScore($modules);
                if ($penalty < $minPenalty) {
                    $msk = $i;
                    $minPenalty = $penalty;
                }
                $this->applyMask($i, $modules, $isFunction); // Undoes the mask due to XOR
            }
        }
        
        assertCondition(0 <= $msk && $msk <= 7);
        $this->mask = $msk;
        $this->applyMask($msk, $modules, $isFunction); // Apply the final choice of mask
        $this->drawFormatBits($msk, $modules); // Overwrite old format bits
        
        $this->modules = $modules;
    }

    /**
     * Returns a QR Code representing the given Unicode text string at the given error correction level.
     * 
     * @param string $text The text to encode
     * @param Ecc $ecl The error correction level
     * @return self A new QrCode instance
     */
    public static function encodeText(string $text, Ecc $ecl): self
    {
        $segs = QrSegment::makeSegments($text);
        return self::encodeSegments($segs, $ecl);
    }

    /**
     * Returns a QR Code representing the given binary data at the given error correction level.
     *
     * @param array<int> $data The binary data (array of bytes 0-255)
     * @param Ecc $ecl The error correction level
     * @return self A new QrCode instance
     */
    public static function encodeBinary(array $data, Ecc $ecl): self
    {
        $seg = QrSegment::makeBytes($data);
        return self::encodeSegments([$seg], $ecl);
    }

    /**
     * Returns a QR Code representing the given segments with the given encoding parameters.
     *
     * @param array<QrSegment> $segs The segments to encode
     * @param Ecc $ecl The error correction level
     * @param int $minVersion The minimum version number (1-40)
     * @param int $maxVersion The maximum version number (1-40)
     * @param int $mask The mask number (-1 for automatic, 0-7 for manual)
     * @param bool $boostEcl Whether to boost error correction level if possible
     * @return self A new QrCode instance
     * @throws \InvalidArgumentException If parameters are invalid
     * @throws \OverflowException If data is too long for any version in range
     */
    public static function encodeSegments(
        array $segs,
        Ecc $ecl,
        int $minVersion = 1,
        int $maxVersion = 40,
        int $mask = -1,
        bool $boostEcl = true
    ): self {
        if (!(self::MIN_VERSION <= $minVersion && $minVersion <= $maxVersion && $maxVersion <= self::MAX_VERSION)
            || $mask < -1 || $mask > 7) {
            throw new \InvalidArgumentException("Invalid value");
        }

        // Find the minimal version number to use
        $version = 0;
        $dataUsedBits = 0;
        for ($version = $minVersion; ; $version++) {
            $dataCapacityBits = self::getNumDataCodewords($version, $ecl) * 8;
            $usedBits = QrSegment::getTotalBits($segs, $version);
            if ($usedBits <= $dataCapacityBits) {
                $dataUsedBits = $usedBits;
                break;
            }
            if ($version >= $maxVersion) {
                throw new \OverflowException("Data too long");
            }
        }

        // Increase the error correction level while the data still fits in the current version number
        foreach ([Ecc::MEDIUM, Ecc::QUARTILE, Ecc::HIGH] as $newEcl) {
            if ($boostEcl && $dataUsedBits <= self::getNumDataCodewords($version, $newEcl) * 8) {
                $ecl = $newEcl;
            }
        }

        // Concatenate all segments to create the data bit string
        $bb = [];
        foreach ($segs as $seg) {
            appendBits($seg->mode->getModeBits(), 4, $bb);
            appendBits($seg->numChars, $seg->mode->getNumCharCountBits($version), $bb);
            foreach ($seg->getData() as $b) {
                $bb[] = $b;
            }
        }
        assertCondition(count($bb) === $dataUsedBits);

        // Add terminator and pad up to a byte if applicable
        $dataCapacityBits = self::getNumDataCodewords($version, $ecl) * 8;
        assertCondition(count($bb) <= $dataCapacityBits);
        appendBits(0, min(4, $dataCapacityBits - count($bb)), $bb);
        appendBits(0, (8 - count($bb) % 8) % 8, $bb);
        assertCondition(count($bb) % 8 === 0);

        // Pad with alternating bytes until data capacity is reached
        for ($padByte = 0xEC; count($bb) < $dataCapacityBits; $padByte ^= 0xEC ^ 0x11) {
            appendBits($padByte, 8, $bb);
        }

        // Pack bits into bytes in big endian
        $dataCodewords = [];
        while (count($dataCodewords) * 8 < count($bb)) {
            $dataCodewords[] = 0;
        }
        foreach ($bb as $i => $b) {
            $dataCodewords[intval($i / 8)] |= $b << (7 - ($i & 7));
        }

        // Create the QR Code object
        return new self($version, $ecl, $dataCodewords, $mask);
    }

    /**
     * Returns the color of the module (pixel) at the given coordinates.
     *
     * @param int $x The x coordinate (0 = left)
     * @param int $y The y coordinate (0 = top)
     * @return bool False for light, true for dark. Returns false if coordinates are out of bounds.
     */
    public function getModule(int $x, int $y): bool
    {
        return 0 <= $x && $x < $this->size && 0 <= $y && $y < $this->size && $this->modules[$y][$x];
    }

    /**
     * Returns the number of data bits that can be stored in a QR Code of the given version number.
     *
     * @param int $ver The version number (1-40)
     * @return int The number of raw data modules
     * @throws \InvalidArgumentException If version is out of range
     */
    private static function getNumRawDataModules(int $ver): int
    {
        if ($ver < self::MIN_VERSION || $ver > self::MAX_VERSION) {
            throw new \InvalidArgumentException("Version number out of range");
        }

        $result = (16 * $ver + 128) * $ver + 64;
        if ($ver >= 2) {
            $numAlign = intval($ver / 7) + 2;
            $result -= (25 * $numAlign - 10) * $numAlign - 55;
            if ($ver >= 7) {
                $result -= 36;
            }
        }
        assertCondition(208 <= $result && $result <= 29648);
        return $result;
    }

    /**
     * Returns the number of 8-bit data (i.e. not error correction) codewords contained in any
     * QR Code of the given version number and error correction level.
     *
     * @param int $ver The version number (1-40)
     * @param Ecc $ecl The error correction level
     * @return int The number of data codewords
     */
    private static function getNumDataCodewords(int $ver, Ecc $ecl): int
    {
        return intval(self::getNumRawDataModules($ver) / 8) -
            self::ECC_CODEWORDS_PER_BLOCK[$ecl->getOrdinal()][$ver] *
            self::NUM_ERROR_CORRECTION_BLOCKS[$ecl->getOrdinal()][$ver];
    }

    /**
     * Reads this object's version field, and draws and marks all function modules.
     *
     * @param array<array<bool>> $modules The modules array
     * @param array<array<bool>> $isFunction The function modules array
     */
    private function drawFunctionPatterns(array &$modules, array &$isFunction): void
    {
        // Draw horizontal and vertical timing patterns
        for ($i = 0; $i < $this->size; $i++) {
            $this->setFunctionModule(6, $i, $i % 2 === 0, $modules, $isFunction);
            $this->setFunctionModule($i, 6, $i % 2 === 0, $modules, $isFunction);
        }

        // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)
        $this->drawFinderPattern(3, 3, $modules, $isFunction);
        $this->drawFinderPattern($this->size - 4, 3, $modules, $isFunction);
        $this->drawFinderPattern(3, $this->size - 4, $modules, $isFunction);

        // Draw numerous alignment patterns
        $alignPatPos = $this->getAlignmentPatternPositions();
        $numAlign = count($alignPatPos);
        for ($i = 0; $i < $numAlign; $i++) {
            for ($j = 0; $j < $numAlign; $j++) {
                // Don't draw on the three finder corners
                if (!($i === 0 && $j === 0 || $i === 0 && $j === $numAlign - 1 || $i === $numAlign - 1 && $j === 0)) {
                    $this->drawAlignmentPattern($alignPatPos[$i], $alignPatPos[$j], $modules, $isFunction);
                }
            }
        }

        // Draw configuration data
        $this->drawFormatBits(0, $modules); // Dummy mask value; overwritten later in the constructor
        $this->drawVersion($modules, $isFunction);
    }

    /**
     * Sets the color of a module and marks it as a function module.
     *
     * @param int $x The x coordinate
     * @param int $y The y coordinate
     * @param bool $isDark Whether the module should be dark
     * @param array<array<bool>> $modules The modules array
     * @param array<array<bool>> $isFunction The function modules array
     */
    private function setFunctionModule(int $x, int $y, bool $isDark, array &$modules, array &$isFunction): void
    {
        $modules[$y][$x] = $isDark;
        $isFunction[$y][$x] = true;
    }

    /**
     * Draws a 9*9 finder pattern including the border separator, with the center module at (x, y).
     *
     * @param int $x The center x coordinate
     * @param int $y The center y coordinate
     * @param array<array<bool>> $modules The modules array
     * @param array<array<bool>> $isFunction The function modules array
     */
    private function drawFinderPattern(int $x, int $y, array &$modules, array &$isFunction): void
    {
        for ($dy = -4; $dy <= 4; $dy++) {
            for ($dx = -4; $dx <= 4; $dx++) {
                $dist = max(abs($dx), abs($dy)); // Chebyshev/infinity norm
                $xx = $x + $dx;
                $yy = $y + $dy;
                if (0 <= $xx && $xx < $this->size && 0 <= $yy && $yy < $this->size) {
                    $this->setFunctionModule($xx, $yy, $dist !== 2 && $dist !== 4, $modules, $isFunction);
                }
            }
        }
    }

    /**
     * Draws a 5*5 alignment pattern, with the center module at (x, y).
     *
     * @param int $x The center x coordinate
     * @param int $y The center y coordinate
     * @param array<array<bool>> $modules The modules array
     * @param array<array<bool>> $isFunction The function modules array
     */
    private function drawAlignmentPattern(int $x, int $y, array &$modules, array &$isFunction): void
    {
        for ($dy = -2; $dy <= 2; $dy++) {
            for ($dx = -2; $dx <= 2; $dx++) {
                $this->setFunctionModule($x + $dx, $y + $dy, max(abs($dx), abs($dy)) !== 1, $modules, $isFunction);
            }
        }
    }

    /**
     * Returns an ascending list of positions of alignment patterns for this version number.
     *
     * @return array<int> Array of alignment pattern positions
     */
    private function getAlignmentPatternPositions(): array
    {
        if ($this->version === 1) {
            return [];
        } else {
            $numAlign = intval($this->version / 7) + 2;
            $step = intval(($this->version * 8 + $numAlign * 3 + 5) / ($numAlign * 4 - 4)) * 2;
            $result = [6];
            for ($pos = $this->size - 7; count($result) < $numAlign; $pos -= $step) {
                array_splice($result, 1, 0, [$pos]);
            }
            return $result;
        }
    }

    /**
     * Draws two copies of the version bits (with its own error correction code),
     * based on this object's version field, iff 7 <= version <= 40.
     *
     * @param array<array<bool>> $modules The modules array
     * @param array<array<bool>> $isFunction The function modules array
     */
    private function drawVersion(array &$modules, array &$isFunction): void
    {
        if ($this->version < 7) {
            return;
        }

        // Calculate error correction code and pack bits
        $rem = $this->version; // version is uint6, in the range [7, 40]
        for ($i = 0; $i < 12; $i++) {
            $rem = ($rem << 1) ^ (($rem >> 11) * 0x1F25);
        }
        $bits = $this->version << 12 | $rem; // uint18
        assertCondition(($bits >> 18) === 0);

        // Draw two copies
        for ($i = 0; $i < 18; $i++) {
            $color = getBit($bits, $i);
            $a = $this->size - 11 + $i % 3;
            $b = intval($i / 3);
            $this->setFunctionModule($a, $b, $color, $modules, $isFunction);
            $this->setFunctionModule($b, $a, $color, $modules, $isFunction);
        }
    }

    /**
     * Returns a new byte string representing the given data with the appropriate error correction
     * codewords appended to it, based on this object's version and error correction level.
     *
     * @param array<int> $data The data codewords
     * @return array<int> The data with error correction codewords
     * @throws \InvalidArgumentException If data length is invalid
     */
    private function addEccAndInterleave(array $data): array
    {
        $ver = $this->version;
        $ecl = $this->errorCorrectionLevel;
        if (count($data) !== self::getNumDataCodewords($ver, $ecl)) {
            throw new \InvalidArgumentException("Invalid argument");
        }

        // Calculate parameter numbers
        $numBlocks = self::NUM_ERROR_CORRECTION_BLOCKS[$ecl->getOrdinal()][$ver];
        $blockEccLen = self::ECC_CODEWORDS_PER_BLOCK[$ecl->getOrdinal()][$ver];
        $rawCodewords = intval(self::getNumRawDataModules($ver) / 8);
        $numShortBlocks = $numBlocks - $rawCodewords % $numBlocks;
        $shortBlockLen = intval($rawCodewords / $numBlocks);

        // Split data into blocks and append ECC to each block
        $blocks = [];
        $rsDiv = self::reedSolomonComputeDivisor($blockEccLen);
        for ($i = 0, $k = 0; $i < $numBlocks; $i++) {
            $dat = array_slice($data, $k, $shortBlockLen - $blockEccLen + ($i < $numShortBlocks ? 0 : 1));
            $k += count($dat);
            $ecc = self::reedSolomonComputeRemainder($dat, $rsDiv);
            if ($i < $numShortBlocks) {
                $dat[] = 0;
            }
            $blocks[] = array_merge($dat, $ecc);
        }

        // Interleave (not concatenate) the bytes from every block into a single sequence
        $result = [];
        for ($i = 0; $i < count($blocks[0]); $i++) {
            foreach ($blocks as $j => $block) {
                // Skip the padding byte in short blocks
                if ($i !== $shortBlockLen - $blockEccLen || $j >= $numShortBlocks) {
                    $result[] = $block[$i];
                }
            }
        }
        assertCondition(count($result) === $rawCodewords);
        return $result;
    }

    /**
     * Returns a Reed-Solomon ECC generator polynomial for the given degree.
     *
     * @param int $degree The degree of the polynomial (1-255)
     * @return array<int> The generator polynomial coefficients
     * @throws \InvalidArgumentException If degree is out of range
     */
    private static function reedSolomonComputeDivisor(int $degree): array
    {
        if ($degree < 1 || $degree > 255) {
            throw new \InvalidArgumentException("Degree out of range");
        }

        // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.
        // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the uint8 array [255, 8, 93].
        $result = [];
        for ($i = 0; $i < $degree - 1; $i++) {
            $result[] = 0;
        }
        $result[] = 1; // Start off with the monomial x^0

        // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),
        // and drop the highest monomial term which is always 1x^degree.
        // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).
        $root = 1;
        for ($i = 0; $i < $degree; $i++) {
            // Multiply the current product by (x - r^i)
            for ($j = 0; $j < count($result); $j++) {
                $result[$j] = self::reedSolomonMultiply($result[$j], $root);
                if ($j + 1 < count($result)) {
                    $result[$j] ^= $result[$j + 1];
                }
            }
            $root = self::reedSolomonMultiply($root, 0x02);
        }
        return $result;
    }

    /**
     * Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.
     *
     * @param array<int> $data The data polynomial
     * @param array<int> $divisor The divisor polynomial
     * @return array<int> The remainder polynomial (error correction codewords)
     */
    private static function reedSolomonComputeRemainder(array $data, array $divisor): array
    {
        $result = array_fill(0, count($divisor), 0);
        foreach ($data as $b) { // Polynomial division
            $factor = $b ^ array_shift($result);
            $result[] = 0;
            foreach ($divisor as $i => $coef) {
                $result[$i] ^= self::reedSolomonMultiply($coef, $factor);
            }
        }
        return $result;
    }

    /**
     * Returns the product of the two given field elements modulo GF(2^8/0x11D).
     *
     * @param int $x First field element (0-255)
     * @param int $y Second field element (0-255)
     * @return int The product (0-255)
     * @throws \InvalidArgumentException If arguments are out of range
     */
    private static function reedSolomonMultiply(int $x, int $y): int
    {
        if (($x >> 8) !== 0 || ($y >> 8) !== 0) {
            throw new \InvalidArgumentException("Byte out of range");
        }
        // Russian peasant multiplication
        $z = 0;
        for ($i = 7; $i >= 0; $i--) {
            $z = ($z << 1) ^ (($z >> 7) * 0x11D);
            $z ^= (($y >> $i) & 1) * $x;
        }
        assertCondition(($z >> 8) === 0);
        return $z;
    }

    /**
     * Draws the given sequence of 8-bit codewords (data and error correction) onto the entire
     * data area of this QR Code. Function modules need to be marked off before this is called.
     *
     * @param array<int> $data The codewords to draw
     * @param array<array<bool>> $modules The modules array
     * @param array<array<bool>> $isFunction The function modules array
     * @throws \InvalidArgumentException If data length is invalid
     */
    private function drawCodewords(array $data, array &$modules, array &$isFunction): void
    {
        if (count($data) !== intval(self::getNumRawDataModules($this->version) / 8)) {
            throw new \InvalidArgumentException("Invalid argument");
        }

        $i = 0; // Bit index into the data
        // Do the funny zigzag scan
        for ($right = $this->size - 1; $right >= 1; $right -= 2) { // Index of right column in each column pair
            if ($right === 6) {
                $right = 5;
            }
            for ($vert = 0; $vert < $this->size; $vert++) { // Vertical counter
                for ($j = 0; $j < 2; $j++) {
                    $x = $right - $j; // Actual x coordinate
                    $upward = (($right + 1) & 2) === 0;
                    $y = $upward ? $this->size - 1 - $vert : $vert; // Actual y coordinate
                    if (!$isFunction[$y][$x] && $i < count($data) * 8) {
                        $modules[$y][$x] = getBit($data[intval($i / 8)], 7 - ($i & 7));
                        $i++;
                    }
                    // If this QR Code has any remainder bits (0 to 7), they were assigned as
                    // 0/false/light by the constructor and are left unchanged by this method
                }
            }
        }
        assertCondition($i === count($data) * 8);
    }

    /**
     * XORs the codeword modules in this QR Code with the given mask pattern.
     * The function modules must be marked and the codeword bits must be drawn
     * before masking. Due to the arithmetic of XOR, calling applyMask() with
     * the same mask value a second time will undo the mask.
     *
     * @param int $mask The mask pattern (0-7)
     * @param array<array<bool>> $modules The modules array
     * @param array<array<bool>> $isFunction The function modules array
     * @throws \InvalidArgumentException If mask is out of range
     */
    private function applyMask(int $mask, array &$modules, array &$isFunction): void
    {
        if ($mask < 0 || $mask > 7) {
            throw new \InvalidArgumentException("Mask value out of range");
        }

        for ($y = 0; $y < $this->size; $y++) {
            for ($x = 0; $x < $this->size; $x++) {
                $invert = false;
                switch ($mask) {
                    case 0: $invert = ($x + $y) % 2 === 0; break;
                    case 1: $invert = $y % 2 === 0; break;
                    case 2: $invert = $x % 3 === 0; break;
                    case 3: $invert = ($x + $y) % 3 === 0; break;
                    case 4: $invert = (intval($x / 3) + intval($y / 2)) % 2 === 0; break;
                    case 5: $invert = $x * $y % 2 + $x * $y % 3 === 0; break;
                    case 6: $invert = ($x * $y % 2 + $x * $y % 3) % 2 === 0; break;
                    case 7: $invert = (($x + $y) % 2 + $x * $y % 3) % 2 === 0; break;
                    default: throw new \RuntimeException("Unreachable");
                }
                if (!$isFunction[$y][$x] && $invert) {
                    $modules[$y][$x] = !$modules[$y][$x];
                }
            }
        }
    }

    /**
     * Draws two copies of the format bits (with its own error correction code)
     * based on the given mask and this object's error correction level field.
     *
     * @param int $mask The mask pattern (0-7)
     * @param array<array<bool>> $modules The modules array
     */
    private function drawFormatBits(int $mask, array &$modules): void
    {
        // Calculate error correction code and pack bits
        $data = $this->errorCorrectionLevel->getFormatBits() << 3 | $mask; // errCorrLvl is uint2, mask is uint3
        $rem = $data;
        for ($i = 0; $i < 10; $i++) {
            $rem = ($rem << 1) ^ (($rem >> 9) * 0x537);
        }
        $bits = ($data << 10 | $rem) ^ 0x5412; // uint15
        assertCondition(($bits >> 15) === 0);

        // Draw first copy
        for ($i = 0; $i <= 5; $i++) {
            $modules[8][$i] = getBit($bits, $i);
        }
        $modules[8][7] = getBit($bits, 6);
        $modules[8][8] = getBit($bits, 7);
        $modules[7][8] = getBit($bits, 8);
        for ($i = 9; $i < 15; $i++) {
            $modules[8][14 - $i] = getBit($bits, $i);
        }

        // Draw second copy
        for ($i = 0; $i < 8; $i++) {
            $modules[8][$this->size - 1 - $i] = getBit($bits, $i);
        }
        for ($i = 8; $i < 15; $i++) {
            $modules[$this->size - 15 + $i][8] = getBit($bits, $i);
        }
        $modules[$this->size - 8][8] = true; // Always dark
    }

    /**
     * Calculates and returns the penalty score based on state of this QR Code's current modules.
     * This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.
     *
     * @param array<array<bool>> $modules The modules array
     * @return int The penalty score
     */
    private function getPenaltyScore(array $modules): int
    {
        $result = 0;

        // Adjacent modules in row having same color, and finder-like patterns
        for ($y = 0; $y < $this->size; $y++) {
            $runColor = false;
            $runX = 0;
            $runHistory = [0, 0, 0, 0, 0, 0, 0];
            for ($x = 0; $x < $this->size; $x++) {
                if ($modules[$y][$x] === $runColor) {
                    $runX++;
                    if ($runX === 5) {
                        $result += self::PENALTY_N1;
                    } elseif ($runX > 5) {
                        $result++;
                    }
                } else {
                    $this->finderPenaltyAddHistory($runX, $runHistory);
                    if (!$runColor) {
                        $result += $this->finderPenaltyCountPatterns($runHistory) * self::PENALTY_N3;
                    }
                    $runColor = $modules[$y][$x];
                    $runX = 1;
                }
            }
            $result += $this->finderPenaltyTerminateAndCount($runColor, $runX, $runHistory) * self::PENALTY_N3;
        }

        // Adjacent modules in column having same color, and finder-like patterns
        for ($x = 0; $x < $this->size; $x++) {
            $runColor = false;
            $runY = 0;
            $runHistory = [0, 0, 0, 0, 0, 0, 0];
            for ($y = 0; $y < $this->size; $y++) {
                if ($modules[$y][$x] === $runColor) {
                    $runY++;
                    if ($runY === 5) {
                        $result += self::PENALTY_N1;
                    } elseif ($runY > 5) {
                        $result++;
                    }
                } else {
                    $this->finderPenaltyAddHistory($runY, $runHistory);
                    if (!$runColor) {
                        $result += $this->finderPenaltyCountPatterns($runHistory) * self::PENALTY_N3;
                    }
                    $runColor = $modules[$y][$x];
                    $runY = 1;
                }
            }
            $result += $this->finderPenaltyTerminateAndCount($runColor, $runY, $runHistory) * self::PENALTY_N3;
        }

        // 2*2 blocks of modules having same color
        for ($y = 0; $y < $this->size - 1; $y++) {
            for ($x = 0; $x < $this->size - 1; $x++) {
                $color = $modules[$y][$x];
                if ($color === $modules[$y][$x + 1] &&
                    $color === $modules[$y + 1][$x] &&
                    $color === $modules[$y + 1][$x + 1]) {
                    $result += self::PENALTY_N2;
                }
            }
        }

        // Balance of dark and light modules
        $dark = 0;
        foreach ($modules as $row) {
            foreach ($row as $color) {
                if ($color) {
                    $dark++;
                }
            }
        }
        $total = $this->size * $this->size; // Note that size is odd, so dark/total != 1/2
        // Compute the smallest integer k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%
        $k = intval(ceil(abs($dark * 20 - $total * 10) / $total)) - 1;
        assertCondition(0 <= $k && $k <= 9);
        $result += $k * self::PENALTY_N4;
        assertCondition(0 <= $result && $result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4
        return $result;
    }

    /**
     * Can only be called immediately after a light run is added, and
     * returns either 0, 1, or 2. A helper function for getPenaltyScore().
     *
     * @param array<int> $runHistory The run history array
     * @return int The penalty count (0, 1, or 2)
     */
    private function finderPenaltyCountPatterns(array $runHistory): int
    {
        $n = $runHistory[1];
        assertCondition($n <= $this->size * 3);
        $core = $n > 0 && $runHistory[2] === $n && $runHistory[3] === $n * 3 && $runHistory[4] === $n && $runHistory[5] === $n;
        return ($core && $runHistory[0] >= $n * 4 && $runHistory[6] >= $n ? 1 : 0)
             + ($core && $runHistory[6] >= $n * 4 && $runHistory[0] >= $n ? 1 : 0);
    }

    /**
     * Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().
     *
     * @param bool $currentRunColor The current run color
     * @param int $currentRunLength The current run length
     * @param array<int> $runHistory The run history array
     * @return int The penalty count
     */
    private function finderPenaltyTerminateAndCount(bool $currentRunColor, int $currentRunLength, array &$runHistory): int
    {
        if ($currentRunColor) { // Terminate dark run
            $this->finderPenaltyAddHistory($currentRunLength, $runHistory);
            $currentRunLength = 0;
        }
        $currentRunLength += $this->size; // Add light border to final run
        $this->finderPenaltyAddHistory($currentRunLength, $runHistory);
        return $this->finderPenaltyCountPatterns($runHistory);
    }

    /**
     * Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().
     *
     * @param int $currentRunLength The current run length
     * @param array<int> $runHistory The run history array (modified by reference)
     */
    private function finderPenaltyAddHistory(int $currentRunLength, array &$runHistory): void
    {
        if ($runHistory[0] === 0) {
            $currentRunLength += $this->size; // Add light border to initial run
        }
        array_pop($runHistory);
        array_unshift($runHistory, $currentRunLength);
    }
}
