<?php

declare(strict_types=1);

namespace QrCodeGen\Tests;

use PHPUnit\Framework\TestCase;
use Qr<PERSON>odeGen\QrCode;
use QrCodeGen\QrSegment;
use QrCodeGen\Ecc;
use QrCodeGen\Mode;

/**
 * Comprehensive test suite for the QR Code library.
 */
class ComprehensiveTest extends TestCase
{
    public function testBasicTextEncoding(): void
    {
        $qr = QrCode::encodeText("Hello, world!", Ecc::MEDIUM);
        
        $this->assertInstanceOf(QrCode::class, $qr);
        $this->assertGreaterThanOrEqual(1, $qr->version);
        $this->assertLessThanOrEqual(40, $qr->version);
        $this->assertSame($qr->version * 4 + 17, $qr->size);
        $this->assertInstanceOf(Ecc::class, $qr->errorCorrectionLevel);
        $this->assertGreaterThanOrEqual(0, $qr->mask);
        $this->assertLessThanOrEqual(7, $qr->mask);
    }

    public function testNumericEncoding(): void
    {
        $qr = QrCode::encodeText("123456789", Ecc::LOW);
        
        $this->assertInstanceOf(QrCode::class, $qr);
        $this->assertSame(Ecc::LOW, $qr->errorCorrectionLevel);
        
        // Should be efficient for numeric data
        $this->assertLessThanOrEqual(2, $qr->version);
    }

    public function testAlphanumericEncoding(): void
    {
        $qr = QrCode::encodeText("HELLO WORLD 123", Ecc::MEDIUM);
        
        $this->assertInstanceOf(QrCode::class, $qr);
        $this->assertSame(Ecc::MEDIUM, $qr->errorCorrectionLevel);
    }

    public function testBinaryEncoding(): void
    {
        $data = [72, 101, 108, 108, 111]; // "Hello"
        $qr = QrCode::encodeBinary($data, Ecc::HIGH);
        
        $this->assertInstanceOf(QrCode::class, $qr);
        $this->assertSame(Ecc::HIGH, $qr->errorCorrectionLevel);
    }

    public function testAllErrorCorrectionLevels(): void
    {
        $text = "Test data for all ECC levels";
        
        foreach ([Ecc::LOW, Ecc::MEDIUM, Ecc::QUARTILE, Ecc::HIGH] as $ecc) {
            $qr = QrCode::encodeText($text, $ecc);
            $this->assertSame($ecc, $qr->errorCorrectionLevel);
        }
    }

    public function testVersionConstraints(): void
    {
        $segments = QrSegment::makeSegments("Test");
        
        // Test minimum version constraint
        $qr = QrCode::encodeSegments($segments, Ecc::LOW, 5, 40);
        $this->assertGreaterThanOrEqual(5, $qr->version);
        
        // Test maximum version constraint
        $qr = QrCode::encodeSegments($segments, Ecc::LOW, 1, 3);
        $this->assertLessThanOrEqual(3, $qr->version);
    }

    public function testManualMask(): void
    {
        $segments = QrSegment::makeSegments("Test mask");
        
        for ($mask = 0; $mask <= 7; $mask++) {
            $qr = QrCode::encodeSegments($segments, Ecc::LOW, 1, 40, $mask);
            $this->assertSame($mask, $qr->mask);
        }
    }

    public function testSegmentCreation(): void
    {
        // Test numeric segment
        $numSeg = QrSegment::makeNumeric("123456");
        $this->assertSame(Mode::NUMERIC, $numSeg->mode);
        $this->assertSame(6, $numSeg->numChars);
        
        // Test alphanumeric segment
        $alphaSeg = QrSegment::makeAlphanumeric("HELLO");
        $this->assertSame(Mode::ALPHANUMERIC, $alphaSeg->mode);
        $this->assertSame(5, $alphaSeg->numChars);
        
        // Test byte segment
        $byteSeg = QrSegment::makeBytes([65, 66, 67]); // "ABC"
        $this->assertSame(Mode::BYTE, $byteSeg->mode);
        $this->assertSame(3, $byteSeg->numChars);
        
        // Test ECI segment
        $eciSeg = QrSegment::makeEci(26); // UTF-8
        $this->assertSame(Mode::ECI, $eciSeg->mode);
        $this->assertSame(0, $eciSeg->numChars);
    }

    public function testSegmentValidation(): void
    {
        // Valid numeric strings
        $this->assertTrue(QrSegment::isNumeric("0"));
        $this->assertTrue(QrSegment::isNumeric("123456789"));
        $this->assertTrue(QrSegment::isNumeric(""));
        
        // Invalid numeric strings
        $this->assertFalse(QrSegment::isNumeric("123a"));
        $this->assertFalse(QrSegment::isNumeric("12.34"));
        $this->assertFalse(QrSegment::isNumeric("12-34"));
        
        // Valid alphanumeric strings
        $this->assertTrue(QrSegment::isAlphanumeric("HELLO"));
        $this->assertTrue(QrSegment::isAlphanumeric("ABC123"));
        $this->assertTrue(QrSegment::isAlphanumeric("HELLO WORLD"));
        $this->assertTrue(QrSegment::isAlphanumeric("$%*+-./:"));
        
        // Invalid alphanumeric strings
        $this->assertFalse(QrSegment::isAlphanumeric("hello")); // lowercase
        $this->assertFalse(QrSegment::isAlphanumeric("ABC@123")); // @ not allowed
        $this->assertFalse(QrSegment::isAlphanumeric("ABC_123")); // _ not allowed
    }

    public function testAutomaticSegmentSelection(): void
    {
        // Should select numeric mode
        $numSegs = QrSegment::makeSegments("123456");
        $this->assertCount(1, $numSegs);
        $this->assertSame(Mode::NUMERIC, $numSegs[0]->mode);
        
        // Should select alphanumeric mode
        $alphaSegs = QrSegment::makeSegments("HELLO123");
        $this->assertCount(1, $alphaSegs);
        $this->assertSame(Mode::ALPHANUMERIC, $alphaSegs[0]->mode);
        
        // Should select byte mode
        $byteSegs = QrSegment::makeSegments("Hello, world!");
        $this->assertCount(1, $byteSegs);
        $this->assertSame(Mode::BYTE, $byteSegs[0]->mode);
        
        // Empty string
        $emptySegs = QrSegment::makeSegments("");
        $this->assertCount(0, $emptySegs);
    }

    public function testModuleAccess(): void
    {
        $qr = QrCode::encodeText("Test", Ecc::LOW);
        
        // Test valid coordinates
        for ($y = 0; $y < $qr->size; $y++) {
            for ($x = 0; $x < $qr->size; $x++) {
                $module = $qr->getModule($x, $y);
                $this->assertIsBool($module);
            }
        }
        
        // Test out-of-bounds coordinates (should return false)
        $this->assertFalse($qr->getModule(-1, 0));
        $this->assertFalse($qr->getModule(0, -1));
        $this->assertFalse($qr->getModule($qr->size, 0));
        $this->assertFalse($qr->getModule(0, $qr->size));
    }

    public function testLargeData(): void
    {
        // Test with large amount of data
        $largeText = str_repeat("A", 1000);
        $qr = QrCode::encodeText($largeText, Ecc::LOW);
        
        $this->assertInstanceOf(QrCode::class, $qr);
        $this->assertGreaterThan(10, $qr->version); // Should require a larger version
    }

    public function testMaximumCapacity(): void
    {
        // Test maximum numeric capacity for version 40, LOW ECC
        $maxNumeric = str_repeat("1", 7089); // Maximum numeric capacity
        $qr = QrCode::encodeText($maxNumeric, Ecc::LOW);
        
        $this->assertSame(40, $qr->version);
        $this->assertSame(Ecc::LOW, $qr->errorCorrectionLevel);
    }

    public function testInvalidInputs(): void
    {
        // Invalid numeric string
        $this->expectException(\InvalidArgumentException::class);
        QrSegment::makeNumeric("123a");
    }

    public function testInvalidAlphanumeric(): void
    {
        // Invalid alphanumeric string
        $this->expectException(\InvalidArgumentException::class);
        QrSegment::makeAlphanumeric("hello"); // lowercase not allowed
    }

    public function testDataTooLong(): void
    {
        // Data too long for any QR code
        $this->expectException(\OverflowException::class);
        $tooLongText = str_repeat("A", 10000);
        QrCode::encodeText($tooLongText, Ecc::HIGH);
    }

    public function testInvalidVersionRange(): void
    {
        // Invalid version range
        $this->expectException(\InvalidArgumentException::class);
        QrCode::encodeSegments(
            QrSegment::makeSegments("Test"),
            Ecc::LOW,
            10, // min version
            5   // max version (invalid: min > max)
        );
    }

    public function testInvalidMask(): void
    {
        // Invalid mask value
        $this->expectException(\InvalidArgumentException::class);
        QrCode::encodeSegments(
            QrSegment::makeSegments("Test"),
            Ecc::LOW,
            1,
            40,
            8 // invalid mask (must be -1 to 7)
        );
    }

    public function testEccBoostingDisabled(): void
    {
        $segments = QrSegment::makeSegments("Short");
        
        // With boosting disabled, should keep the requested ECC level
        $qr = QrCode::encodeSegments($segments, Ecc::LOW, 1, 40, -1, false);
        $this->assertSame(Ecc::LOW, $qr->errorCorrectionLevel);
    }

    public function testConsistentOutput(): void
    {
        // Same input should produce same output
        $text = "Consistency test";
        $qr1 = QrCode::encodeText($text, Ecc::MEDIUM);
        $qr2 = QrCode::encodeText($text, Ecc::MEDIUM);
        
        $this->assertSame($qr1->version, $qr2->version);
        $this->assertSame($qr1->size, $qr2->size);
        $this->assertSame($qr1->errorCorrectionLevel, $qr2->errorCorrectionLevel);
        $this->assertSame($qr1->mask, $qr2->mask);
        
        // All modules should be identical
        for ($y = 0; $y < $qr1->size; $y++) {
            for ($x = 0; $x < $qr1->size; $x++) {
                $this->assertSame(
                    $qr1->getModule($x, $y),
                    $qr2->getModule($x, $y),
                    "Module at ($x, $y) differs between identical inputs"
                );
            }
        }
    }

    public function testFinderPatterns(): void
    {
        $qr = QrCode::encodeText("Test finder patterns", Ecc::LOW);
        
        // Check that finder patterns exist at expected locations
        // Top-left finder pattern
        $this->assertTrue($qr->getModule(0, 0));
        $this->assertTrue($qr->getModule(6, 0));
        $this->assertTrue($qr->getModule(0, 6));
        $this->assertTrue($qr->getModule(6, 6));
        
        // Top-right finder pattern
        $this->assertTrue($qr->getModule($qr->size - 7, 0));
        $this->assertTrue($qr->getModule($qr->size - 1, 0));
        $this->assertTrue($qr->getModule($qr->size - 7, 6));
        $this->assertTrue($qr->getModule($qr->size - 1, 6));
        
        // Bottom-left finder pattern
        $this->assertTrue($qr->getModule(0, $qr->size - 7));
        $this->assertTrue($qr->getModule(6, $qr->size - 7));
        $this->assertTrue($qr->getModule(0, $qr->size - 1));
        $this->assertTrue($qr->getModule(6, $qr->size - 1));
    }

    public function testTimingPatterns(): void
    {
        $qr = QrCode::encodeText("Test timing patterns", Ecc::LOW);
        
        // Check timing patterns (alternating dark/light on row/column 6)
        for ($i = 8; $i < $qr->size - 8; $i++) {
            $expected = ($i % 2) === 0;
            $this->assertSame($expected, $qr->getModule(6, $i), "Horizontal timing pattern at position $i");
            $this->assertSame($expected, $qr->getModule($i, 6), "Vertical timing pattern at position $i");
        }
    }
}
