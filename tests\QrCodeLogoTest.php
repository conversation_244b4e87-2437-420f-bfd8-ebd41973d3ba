<?php

declare(strict_types=1);

namespace QrCodeGen\Tests;

use PHPUnit\Framework\TestCase;
use Qr<PERSON>odeGen\QrCode;
use QrCodeGen\QrCodeLogo;
use QrCodeGen\LogoConfig;
use QrCodeGen\Ecc;
use QrCodeGen\Exceptions\LogoException;

/**
 * Unit tests for QrCodeLogo class.
 * 
 * @covers \QrCodeGen\QrCodeLogo
 */
class QrCodeLogoTest extends TestCase
{
    private QrCode $qrCode;
    private string $validSvg;
    private LogoConfig $validConfig;

    protected function setUp(): void
    {
        $this->qrCode = QrCode::encodeText("Test QR Code", Ecc::MEDIUM);
        $this->validSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="40" fill="#007bff"/>
        </svg>';
        $this->validConfig = new LogoConfig($this->validSvg, sizePercentage: 0.15);
    }

    public function testConstructorWithValidParameters(): void
    {
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        
        $this->assertSame($this->qrCode, $qrWithLogo->getQrCode());
        $this->assertSame($this->validConfig, $qrWithLogo->getConfig());
    }

    public function testConstructorValidatesConfigurationAgainstEcc(): void
    {
        $lowEccQr = QrCode::encodeText("Test", Ecc::LOW);
        $largeLogo = new LogoConfig($this->validSvg, sizePercentage: 0.25); // Too large for LOW ECC
        
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::SIZE_CONSTRAINT_VIOLATION);
        
        new QrCodeLogo($lowEccQr, $largeLogo);
    }

    public function testToSvgGeneratesValidSvg(): void
    {
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        $svg = $qrWithLogo->toSvg();
        
        // Basic SVG structure validation
        $this->assertStringStartsWith('<?xml version="1.0"', $svg);
        $this->assertStringContainsString('<svg xmlns="http://www.w3.org/2000/svg"', $svg);
        $this->assertStringContainsString('</svg>', $svg);
        
        // Check for QR code elements
        $this->assertStringContainsString('<rect width="100%" height="100%"', $svg);
        $this->assertStringContainsString('<path d="', $svg);
        
        // Check for logo elements
        $this->assertStringContainsString('<g transform="translate(', $svg);
    }

    public function testToSvgWithCustomColors(): void
    {
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        $svg = $qrWithLogo->toSvg(
            border: 6,
            lightColor: '#F0F0F0',
            darkColor: '#333333'
        );
        
        $this->assertStringContainsString('fill="#F0F0F0"', $svg);
        $this->assertStringContainsString('fill="#333333"', $svg);
    }

    public function testToSvgWithMinimumBorder(): void
    {
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        
        // Test with border less than minimum (should be adjusted to 4)
        $svg = $qrWithLogo->toSvg(border: 2);
        
        $expectedSize = $this->qrCode->size + 8; // 4 * 2 for minimum border
        $this->assertStringContainsString("viewBox=\"0 0 $expectedSize $expectedSize\"", $svg);
    }

    public function testToSvgWithBackground(): void
    {
        $configWithBackground = new LogoConfig(
            logoSource: $this->validSvg,
            sizePercentage: 0.15,
            addBackground: true,
            backgroundColor: '#FFFFFF',
            backgroundPadding: 3
        );
        
        $qrWithLogo = new QrCodeLogo($this->qrCode, $configWithBackground);
        $svg = $qrWithLogo->toSvg();
        
        // Should contain background rectangle for logo
        $this->assertStringContainsString('<rect x=', $svg);
        $this->assertStringContainsString('fill="#FFFFFF"', $svg);
    }

    public function testToSvgWithoutBackground(): void
    {
        $configWithoutBackground = new LogoConfig(
            logoSource: $this->validSvg,
            sizePercentage: 0.15,
            addBackground: false
        );
        
        $qrWithLogo = new QrCodeLogo($this->qrCode, $configWithoutBackground);
        $svg = $qrWithLogo->toSvg();
        
        // Should not contain extra background rectangle (only the main background)
        $backgroundCount = substr_count($svg, '<rect');
        $this->assertEquals(1, $backgroundCount); // Only main background
    }

    public function testToSvgWithOffsets(): void
    {
        $configWithOffsets = new LogoConfig(
            logoSource: $this->validSvg,
            sizePercentage: 0.15,
            offsetX: 0.5,
            offsetY: -0.3
        );
        
        $qrWithLogo = new QrCodeLogo($this->qrCode, $configWithOffsets);
        $svg = $qrWithLogo->toSvg();
        
        // Should contain transform with offset coordinates
        $this->assertStringContainsString('<g transform="translate(', $svg);
    }

    public function testToPngRequiresGdExtension(): void
    {
        if (extension_loaded('gd')) {
            $this->markTestSkipped('GD extension is loaded, cannot test missing extension scenario');
        }
        
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::SVG_PROCESSING_ERROR);
        
        $qrWithLogo->toPng();
    }

    public function testToPngWithGdExtension(): void
    {
        if (!extension_loaded('gd')) {
            $this->markTestSkipped('GD extension not available');
        }
        
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        $pngData = $qrWithLogo->toPng(scale: 4, border: 4);
        
        $this->assertIsString($pngData);
        $this->assertGreaterThan(0, strlen($pngData));
        
        // Verify it's valid PNG data
        $imageInfo = getimagesizefromstring($pngData);
        $this->assertNotFalse($imageInfo);
        $this->assertEquals('image/png', $imageInfo['mime']);
    }

    public function testToJpegRequiresGdExtension(): void
    {
        if (extension_loaded('gd')) {
            $this->markTestSkipped('GD extension is loaded, cannot test missing extension scenario');
        }
        
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::SVG_PROCESSING_ERROR);
        
        $qrWithLogo->toJpeg();
    }

    public function testToJpegWithGdExtension(): void
    {
        if (!extension_loaded('gd')) {
            $this->markTestSkipped('GD extension not available');
        }
        
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        $jpegData = $qrWithLogo->toJpeg(scale: 4, border: 4, quality: 85);
        
        $this->assertIsString($jpegData);
        $this->assertGreaterThan(0, strlen($jpegData));
        
        // Verify it's valid JPEG data
        $imageInfo = getimagesizefromstring($jpegData);
        $this->assertNotFalse($imageInfo);
        $this->assertEquals('image/jpeg', $imageInfo['mime']);
    }

    public function testDifferentErrorCorrectionLevels(): void
    {
        $testData = "Test data for ECC levels";
        
        $eccLevels = [
            ['ecc' => Ecc::LOW, 'maxSize' => 0.15],
            ['ecc' => Ecc::MEDIUM, 'maxSize' => 0.20],
            ['ecc' => Ecc::QUARTILE, 'maxSize' => 0.25],
            ['ecc' => Ecc::HIGH, 'maxSize' => 0.30],
        ];
        
        foreach ($eccLevels as $level) {
            $qr = QrCode::encodeText($testData, $level['ecc']);
            $config = new LogoConfig($this->validSvg, sizePercentage: $level['maxSize']);
            
            // Should not throw exception
            $qrWithLogo = new QrCodeLogo($qr, $config);
            $svg = $qrWithLogo->toSvg();
            
            $this->assertStringContainsString('<svg', $svg);
            $this->assertStringContainsString('</svg>', $svg);
        }
    }

    public function testComplexSvgLogo(): void
    {
        $complexSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
            <defs>
                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:rgb(255,255,0);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
                </linearGradient>
            </defs>
            <ellipse cx="100" cy="70" rx="85" ry="55" fill="url(#grad1)" />
            <text x="100" y="80" text-anchor="middle" fill="white" font-size="16">LOGO</text>
        </svg>';
        
        $config = new LogoConfig($complexSvg, sizePercentage: 0.18);
        $qrWithLogo = new QrCodeLogo($this->qrCode, $config);
        $svg = $qrWithLogo->toSvg();
        
        // Should handle complex SVG without errors
        $this->assertStringContainsString('<svg', $svg);
        $this->assertStringContainsString('linearGradient', $svg);
        $this->assertStringContainsString('ellipse', $svg);
        $this->assertStringContainsString('text', $svg);
    }

    public function testSvgViewBoxExtraction(): void
    {
        $svgWithViewBox = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 150 150">
            <rect x="10" y="10" width="130" height="130" fill="blue"/>
        </svg>';
        
        $config = new LogoConfig($svgWithViewBox, sizePercentage: 0.15);
        $qrWithLogo = new QrCodeLogo($this->qrCode, $config);
        $svg = $qrWithLogo->toSvg();
        
        // Should preserve viewBox in embedded SVG
        $this->assertStringContainsString('viewBox="0 0 150 150"', $svg);
    }

    public function testSvgWithoutViewBox(): void
    {
        $svgWithoutViewBox = '<svg xmlns="http://www.w3.org/2000/svg" width="120" height="80">
            <rect x="10" y="10" width="100" height="60" fill="green"/>
        </svg>';
        
        $config = new LogoConfig($svgWithoutViewBox, sizePercentage: 0.15);
        $qrWithLogo = new QrCodeLogo($this->qrCode, $config);
        $svg = $qrWithLogo->toSvg();
        
        // Should create fallback viewBox
        $this->assertStringContainsString('viewBox=', $svg);
    }

    public function testLogoPositionCalculation(): void
    {
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        $svg = $qrWithLogo->toSvg(border: 4);
        
        // Logo should be positioned in center area
        $this->assertStringContainsString('<g transform="translate(', $svg);
        
        // Extract transform coordinates to verify centering
        preg_match('/translate\(([^,]+),([^)]+)\)/', $svg, $matches);
        $this->assertCount(3, $matches);
        
        $x = (float)$matches[1];
        $y = (float)$matches[2];
        
        $totalSize = $this->qrCode->size + 8; // border * 2
        $center = $totalSize / 2;
        
        // Logo position should be near center (allowing for logo size offset)
        $this->assertGreaterThan($center - 10, $x);
        $this->assertLessThan($center + 10, $x);
        $this->assertGreaterThan($center - 10, $y);
        $this->assertLessThan($center + 10, $y);
    }

    public function testModuleExclusionInLogoArea(): void
    {
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        $svg = $qrWithLogo->toSvg();
        
        // The SVG should contain QR modules but some should be excluded in logo area
        $this->assertStringContainsString('<path d="', $svg);
        
        // Count the number of module commands in the path
        preg_match('/<path d="([^"]+)"/', $svg, $matches);
        $this->assertCount(2, $matches);
        
        $pathData = $matches[1];
        $moduleCount = substr_count($pathData, 'M');
        
        // Should have fewer modules than total QR code modules due to logo exclusion
        $totalModules = 0;
        for ($y = 0; $y < $this->qrCode->size; $y++) {
            for ($x = 0; $x < $this->qrCode->size; $x++) {
                if ($this->qrCode->getModule($x, $y)) {
                    $totalModules++;
                }
            }
        }
        
        $this->assertLessThan($totalModules, $moduleCount);
    }

    public function testGettersReturnCorrectInstances(): void
    {
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        
        $this->assertSame($this->qrCode, $qrWithLogo->getQrCode());
        $this->assertSame($this->validConfig, $qrWithLogo->getConfig());
    }

    public function testMultipleOutputFormatsConsistency(): void
    {
        if (!extension_loaded('gd')) {
            $this->markTestSkipped('GD extension not available');
        }
        
        $qrWithLogo = new QrCodeLogo($this->qrCode, $this->validConfig);
        
        $svg = $qrWithLogo->toSvg();
        $png = $qrWithLogo->toPng(scale: 4);
        $jpeg = $qrWithLogo->toJpeg(scale: 4, quality: 90);
        
        // All formats should produce non-empty output
        $this->assertGreaterThan(0, strlen($svg));
        $this->assertGreaterThan(0, strlen($png));
        $this->assertGreaterThan(0, strlen($jpeg));
        
        // Verify format signatures
        $this->assertStringStartsWith('<?xml', $svg);
        $this->assertEquals("\x89PNG", substr($png, 0, 4));
        $this->assertEquals("\xFF\xD8\xFF", substr($jpeg, 0, 3));
    }
}
