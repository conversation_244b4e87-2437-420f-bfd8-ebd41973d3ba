<?php

declare(strict_types=1);

require_once __DIR__ . '/../src/functions.php';
require_once __DIR__ . '/../src/Ecc.php';
require_once __DIR__ . '/../src/Mode.php';
require_once __DIR__ . '/../src/QrSegment.php';
require_once __DIR__ . '/../src/QrCode.php';

use QrCodeGen\QrCode;
use QrCodeGen\Ecc;

/**
 * Converts a QR Code to an SVG string.
 * 
 * @param QrCode $qr The QR code to convert
 * @param int $border The border size in modules
 * @param string $lightColor The color for light modules
 * @param string $darkColor The color for dark modules
 * @return string The SVG markup
 */
function toSvg(QrCode $qr, int $border = 4, string $lightColor = "#FFFFFF", string $darkColor = "#000000"): string
{
    if ($border < 0) {
        throw new InvalidArgumentException("Border must be non-negative");
    }
    
    $size = $qr->size + $border * 2;
    $svg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $svg .= "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n";
    $svg .= "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 $size $size\" stroke=\"none\">\n";
    $svg .= "\t<rect width=\"100%\" height=\"100%\" fill=\"$lightColor\"/>\n";
    $svg .= "\t<path d=\"";
    
    for ($y = 0; $y < $qr->size; $y++) {
        for ($x = 0; $x < $qr->size; $x++) {
            if ($qr->getModule($x, $y)) {
                $px = $x + $border;
                $py = $y + $border;
                $svg .= "M$px,$py h1v1h-1z";
            }
        }
    }
    
    $svg .= "\" fill=\"$darkColor\"/>\n";
    $svg .= "</svg>\n";
    return $svg;
}

/**
 * Converts a QR Code to an SVG string with individual rectangles (alternative method).
 * 
 * @param QrCode $qr The QR code to convert
 * @param int $border The border size in modules
 * @param string $lightColor The color for light modules
 * @param string $darkColor The color for dark modules
 * @return string The SVG markup
 */
function toSvgRects(QrCode $qr, int $border = 4, string $lightColor = "#FFFFFF", string $darkColor = "#000000"): string
{
    if ($border < 0) {
        throw new InvalidArgumentException("Border must be non-negative");
    }
    
    $size = $qr->size + $border * 2;
    $svg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $svg .= "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n";
    $svg .= "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 $size $size\" stroke=\"none\">\n";
    $svg .= "\t<rect width=\"100%\" height=\"100%\" fill=\"$lightColor\"/>\n";
    
    for ($y = 0; $y < $qr->size; $y++) {
        for ($x = 0; $x < $qr->size; $x++) {
            if ($qr->getModule($x, $y)) {
                $px = $x + $border;
                $py = $y + $border;
                $svg .= "\t<rect x=\"$px\" y=\"$py\" width=\"1\" height=\"1\" fill=\"$darkColor\"/>\n";
            }
        }
    }
    
    $svg .= "</svg>\n";
    return $svg;
}

/**
 * Converts a QR Code to a PNG data URL (requires GD extension).
 * 
 * @param QrCode $qr The QR code to convert
 * @param int $scale The scale factor (pixels per module)
 * @param int $border The border size in modules
 * @return string The PNG data URL
 */
function toPngDataUrl(QrCode $qr, int $scale = 8, int $border = 4): string
{
    if (!extension_loaded('gd')) {
        throw new RuntimeException("GD extension is required for PNG output");
    }
    
    $size = ($qr->size + $border * 2) * $scale;
    $image = imagecreate($size, $size);
    
    $white = imagecolorallocate($image, 255, 255, 255);
    $black = imagecolorallocate($image, 0, 0, 0);
    
    imagefill($image, 0, 0, $white);
    
    for ($y = 0; $y < $qr->size; $y++) {
        for ($x = 0; $x < $qr->size; $x++) {
            if ($qr->getModule($x, $y)) {
                $px = ($x + $border) * $scale;
                $py = ($y + $border) * $scale;
                imagefilledrectangle($image, $px, $py, $px + $scale - 1, $py + $scale - 1, $black);
            }
        }
    }
    
    ob_start();
    imagepng($image);
    $pngData = ob_get_clean();
    imagedestroy($image);
    
    return 'data:image/png;base64,' . base64_encode($pngData);
}

echo "QR Code Generator - SVG Output Examples\n";
echo "=======================================\n\n";

// Example 1: Basic SVG output
echo "Example 1: Basic SVG output\n";
echo "---------------------------\n";
$qr1 = QrCode::encodeText("Hello, SVG!", Ecc::MEDIUM);
$svg1 = toSvg($qr1);
file_put_contents(__DIR__ . '/output_basic.svg', $svg1);
echo "Generated: output_basic.svg\n";
echo "Text: 'Hello, SVG!'\n";
echo "Size: {$qr1->size}×{$qr1->size} modules\n\n";

// Example 2: Custom colors
echo "Example 2: Custom colors\n";
echo "-----------------------\n";
$qr2 = QrCode::encodeText("https://example.com", Ecc::HIGH);
$svg2 = toSvg($qr2, 4, "#F0F8FF", "#4169E1"); // Light blue background, royal blue foreground
file_put_contents(__DIR__ . '/output_colored.svg', $svg2);
echo "Generated: output_colored.svg\n";
echo "Colors: Light blue background, royal blue foreground\n";
echo "URL: https://example.com\n\n";

// Example 3: Large border
echo "Example 3: Large border\n";
echo "----------------------\n";
$qr3 = QrCode::encodeText("QR Code with large border", Ecc::LOW);
$svg3 = toSvg($qr3, 8); // 8-module border
file_put_contents(__DIR__ . '/output_large_border.svg', $svg3);
echo "Generated: output_large_border.svg\n";
echo "Border: 8 modules\n";
echo "Text: 'QR Code with large border'\n\n";

// Example 4: Rectangle-based SVG (alternative method)
echo "Example 4: Rectangle-based SVG\n";
echo "------------------------------\n";
$qr4 = QrCode::encodeText("Rectangle method", Ecc::QUARTILE);
$svg4 = toSvgRects($qr4, 4, "#FFFACD", "#8B4513"); // Light yellow background, saddle brown foreground
file_put_contents(__DIR__ . '/output_rects.svg', $svg4);
echo "Generated: output_rects.svg\n";
echo "Method: Individual rectangles\n";
echo "Colors: Light yellow background, saddle brown foreground\n\n";

// Example 5: Multiple QR codes in one SVG
echo "Example 5: Multiple QR codes in one SVG\n";
echo "---------------------------------------\n";
$qrCodes = [
    QrCode::encodeText("QR 1", Ecc::LOW),
    QrCode::encodeText("QR 2", Ecc::MEDIUM),
    QrCode::encodeText("QR 3", Ecc::HIGH)
];

$combinedSvg = createMultiQrSvg($qrCodes);
file_put_contents(__DIR__ . '/output_multiple.svg', $combinedSvg);
echo "Generated: output_multiple.svg\n";
echo "Contains 3 QR codes side by side\n\n";

// Example 6: PNG output (if GD is available)
echo "Example 6: PNG output\n";
echo "--------------------\n";
if (extension_loaded('gd')) {
    $qr6 = QrCode::encodeText("PNG output test", Ecc::MEDIUM);
    $pngDataUrl = toPngDataUrl($qr6, 10, 4); // 10 pixels per module, 4-module border
    
    // Save as actual PNG file
    $pngData = base64_decode(substr($pngDataUrl, strlen('data:image/png;base64,')));
    file_put_contents(__DIR__ . '/output_test.png', $pngData);
    
    echo "Generated: output_test.png\n";
    echo "Scale: 10 pixels per module\n";
    echo "Data URL length: " . strlen($pngDataUrl) . " characters\n";
} else {
    echo "GD extension not available - PNG output skipped\n";
}
echo "\n";

// Example 7: Responsive SVG with CSS
echo "Example 7: Responsive SVG with CSS\n";
echo "----------------------------------\n";
$qr7 = QrCode::encodeText("Responsive QR Code", Ecc::MEDIUM);
$responsiveSvg = createResponsiveSvg($qr7);
file_put_contents(__DIR__ . '/output_responsive.svg', $responsiveSvg);
echo "Generated: output_responsive.svg\n";
echo "Features: Responsive design with CSS styling\n\n";

echo "All SVG examples completed!\n";
echo "Check the generated files in the examples/ directory.\n";

/**
 * Creates an SVG containing multiple QR codes side by side.
 */
function createMultiQrSvg(array $qrCodes): string
{
    $border = 4;
    $spacing = 2;
    $maxSize = 0;
    
    // Find the largest QR code size
    foreach ($qrCodes as $qr) {
        $maxSize = max($maxSize, $qr->size);
    }
    
    $qrWidth = $maxSize + $border * 2;
    $totalWidth = count($qrCodes) * $qrWidth + (count($qrCodes) - 1) * $spacing;
    $totalHeight = $qrWidth;
    
    $svg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $svg .= "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 $totalWidth $totalHeight\">\n";
    $svg .= "\t<rect width=\"100%\" height=\"100%\" fill=\"white\"/>\n";
    
    foreach ($qrCodes as $i => $qr) {
        $offsetX = $i * ($qrWidth + $spacing);
        $offsetY = 0;
        
        // Center smaller QR codes
        $centerOffsetX = ($maxSize - $qr->size) / 2;
        $centerOffsetY = ($maxSize - $qr->size) / 2;
        
        $svg .= "\t<g transform=\"translate(" . ($offsetX + $border + $centerOffsetX) . "," . ($offsetY + $border + $centerOffsetY) . ")\">\n";
        
        for ($y = 0; $y < $qr->size; $y++) {
            for ($x = 0; $x < $qr->size; $x++) {
                if ($qr->getModule($x, $y)) {
                    $svg .= "\t\t<rect x=\"$x\" y=\"$y\" width=\"1\" height=\"1\" fill=\"black\"/>\n";
                }
            }
        }
        
        $svg .= "\t</g>\n";
    }
    
    $svg .= "</svg>\n";
    return $svg;
}

/**
 * Creates a responsive SVG with CSS styling.
 */
function createResponsiveSvg(QrCode $qr): string
{
    $border = 4;
    $size = $qr->size + $border * 2;
    
    $svg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $svg .= "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 $size $size\" class=\"qr-code\">\n";
    $svg .= "\t<style>\n";
    $svg .= "\t\t.qr-code { max-width: 100%; height: auto; }\n";
    $svg .= "\t\t.qr-background { fill: #ffffff; }\n";
    $svg .= "\t\t.qr-foreground { fill: #000000; }\n";
    $svg .= "\t\t@media (prefers-color-scheme: dark) {\n";
    $svg .= "\t\t\t.qr-background { fill: #1a1a1a; }\n";
    $svg .= "\t\t\t.qr-foreground { fill: #ffffff; }\n";
    $svg .= "\t\t}\n";
    $svg .= "\t</style>\n";
    $svg .= "\t<rect width=\"100%\" height=\"100%\" class=\"qr-background\"/>\n";
    $svg .= "\t<path d=\"";
    
    for ($y = 0; $y < $qr->size; $y++) {
        for ($x = 0; $x < $qr->size; $x++) {
            if ($qr->getModule($x, $y)) {
                $px = $x + $border;
                $py = $y + $border;
                $svg .= "M$px,$py h1v1h-1z";
            }
        }
    }
    
    $svg .= "\" class=\"qr-foreground\"/>\n";
    $svg .= "</svg>\n";
    return $svg;
}
