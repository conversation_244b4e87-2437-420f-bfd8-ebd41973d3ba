<?php

declare(strict_types=1);

namespace QrCodeGen\Tests;

use PHPUnit\Framework\TestCase;
use QrCodeGen\LogoConfig;
use QrCodeGen\Ecc;
use QrCodeGen\Exceptions\LogoException;

/**
 * Unit tests for LogoConfig class.
 * 
 * @covers \QrCodeGen\LogoConfig
 */
class LogoConfigTest extends TestCase
{
    private string $validSvg;
    private string $tempFile;

    protected function setUp(): void
    {
        $this->validSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="40" fill="#007bff"/>
        </svg>';
        
        $this->tempFile = tempnam(sys_get_temp_dir(), 'qr_logo_test_');
        file_put_contents($this->tempFile, $this->validSvg);
    }

    protected function tearDown(): void
    {
        if (file_exists($this->tempFile)) {
            unlink($this->tempFile);
        }
    }

    public function testConstructorWithValidParameters(): void
    {
        $config = new LogoConfig(
            logoSource: $this->validSvg,
            sizePercentage: 0.20,
            addBackground: true,
            backgroundColor: '#FFFFFF',
            backgroundPadding: 2
        );

        $this->assertEquals($this->validSvg, $config->getLogoSource());
        $this->assertEquals(0.20, $config->getSizePercentage());
        $this->assertTrue($config->shouldAddBackground());
        $this->assertEquals('#FFFFFF', $config->getBackgroundColor());
        $this->assertEquals(2, $config->getBackgroundPadding());
        $this->assertEquals(0.0, $config->getOffsetX());
        $this->assertEquals(0.0, $config->getOffsetY());
        $this->assertTrue($config->shouldMaintainAspectRatio());
    }

    public function testConstructorWithDefaultParameters(): void
    {
        $config = new LogoConfig($this->validSvg);

        $this->assertEquals(0.20, $config->getSizePercentage());
        $this->assertTrue($config->shouldAddBackground());
        $this->assertEquals('#FFFFFF', $config->getBackgroundColor());
        $this->assertEquals(2, $config->getBackgroundPadding());
    }

    public function testConstructorWithCustomOffsets(): void
    {
        $config = new LogoConfig(
            logoSource: $this->validSvg,
            offsetX: 0.5,
            offsetY: -0.3
        );

        $this->assertEquals(0.5, $config->getOffsetX());
        $this->assertEquals(-0.3, $config->getOffsetY());
    }

    public function testInvalidLogoSourceThrowsException(): void
    {
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_CONFIGURATION);
        
        new LogoConfig('');
    }

    public function testInvalidSizePercentageThrowsException(): void
    {
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_CONFIGURATION);
        
        new LogoConfig($this->validSvg, sizePercentage: 1.5);
    }

    public function testSizePercentageTooSmallThrowsException(): void
    {
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_CONFIGURATION);
        
        new LogoConfig($this->validSvg, sizePercentage: 0.01);
    }

    public function testInvalidBackgroundColorThrowsException(): void
    {
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_CONFIGURATION);
        
        new LogoConfig($this->validSvg, backgroundColor: 'invalid-color');
    }

    public function testValidBackgroundColors(): void
    {
        $validColors = ['#FFFFFF', '#fff', '#123456', 'red', 'blue', 'transparent'];
        
        foreach ($validColors as $color) {
            $config = new LogoConfig($this->validSvg, backgroundColor: $color);
            $this->assertEquals($color, $config->getBackgroundColor());
        }
    }

    public function testInvalidBackgroundPaddingThrowsException(): void
    {
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_CONFIGURATION);
        
        new LogoConfig($this->validSvg, backgroundPadding: -1);
    }

    public function testBackgroundPaddingTooLargeThrowsException(): void
    {
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_CONFIGURATION);
        
        new LogoConfig($this->validSvg, backgroundPadding: 15);
    }

    public function testInvalidOffsetThrowsException(): void
    {
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_CONFIGURATION);
        
        new LogoConfig($this->validSvg, offsetX: 1.5);
    }

    public function testValidateForErrorCorrectionLevelSuccess(): void
    {
        $config = new LogoConfig($this->validSvg, sizePercentage: 0.15);
        
        // Should not throw for LOW error correction with 15% logo
        $config->validateForErrorCorrectionLevel(Ecc::LOW);
        $this->assertTrue(true); // Test passes if no exception is thrown
    }

    public function testValidateForErrorCorrectionLevelFailure(): void
    {
        $config = new LogoConfig($this->validSvg, sizePercentage: 0.25);
        
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::SIZE_CONSTRAINT_VIOLATION);
        
        // Should throw for LOW error correction with 25% logo
        $config->validateForErrorCorrectionLevel(Ecc::LOW);
    }

    public function testGetMaxLogoSizeForDifferentEccLevels(): void
    {
        $this->assertEquals(0.15, LogoConfig::getMaxLogoSize(Ecc::LOW));
        $this->assertEquals(0.20, LogoConfig::getMaxLogoSize(Ecc::MEDIUM));
        $this->assertEquals(0.25, LogoConfig::getMaxLogoSize(Ecc::QUARTILE));
        $this->assertEquals(0.30, LogoConfig::getMaxLogoSize(Ecc::HIGH));
    }

    public function testGetLogoSvgContentFromString(): void
    {
        $config = new LogoConfig($this->validSvg);
        $content = $config->getLogoSvgContent();
        
        $this->assertEquals($this->validSvg, $content);
    }

    public function testGetLogoSvgContentFromFile(): void
    {
        $config = new LogoConfig($this->tempFile);
        $content = $config->getLogoSvgContent();
        
        $this->assertEquals($this->validSvg, $content);
    }

    public function testGetLogoSvgContentFromNonExistentFile(): void
    {
        $config = new LogoConfig('/non/existent/file.svg');
        
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::FILE_SYSTEM_ERROR);
        
        $config->getLogoSvgContent();
    }

    public function testGetLogoSvgContentFromInvalidSvg(): void
    {
        $invalidSvg = '<div>Not an SVG</div>';
        $config = new LogoConfig($invalidSvg);
        
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_SVG);
        
        $config->getLogoSvgContent();
    }

    public function testGetLogoSvgContentWithDangerousContent(): void
    {
        $dangerousSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
            <script>alert("xss")</script>
            <circle cx="50" cy="50" r="40" fill="red"/>
        </svg>';
        
        $config = new LogoConfig($dangerousSvg);
        
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_SVG);
        
        $config->getLogoSvgContent();
    }

    public function testGetLogoSvgContentValidatesJavaScript(): void
    {
        $dangerousSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" onload="alert(1)">
            <circle cx="50" cy="50" r="40" fill="red"/>
        </svg>';
        
        $config = new LogoConfig($dangerousSvg);
        
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_SVG);
        
        $config->getLogoSvgContent();
    }

    public function testSvgContentValidation(): void
    {
        // Test missing SVG tags
        $this->expectException(LogoException::class);
        $this->expectExceptionCode(LogoException::INVALID_SVG);
        
        $config = new LogoConfig('<circle cx="50" cy="50" r="40" fill="red"/>');
        $config->getLogoSvgContent();
    }

    public function testComplexValidSvg(): void
    {
        $complexSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
            <defs>
                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:rgb(255,255,0);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
                </linearGradient>
            </defs>
            <ellipse cx="100" cy="70" rx="85" ry="55" fill="url(#grad1)" />
            <text x="100" y="80" text-anchor="middle" fill="white" font-size="16">LOGO</text>
        </svg>';
        
        $config = new LogoConfig($complexSvg);
        $content = $config->getLogoSvgContent();
        
        $this->assertEquals($complexSvg, $content);
    }

    public function testBoundaryValues(): void
    {
        // Test minimum valid size
        $config = new LogoConfig($this->validSvg, sizePercentage: 0.05);
        $this->assertEquals(0.05, $config->getSizePercentage());
        
        // Test maximum valid size
        $config = new LogoConfig($this->validSvg, sizePercentage: 0.30);
        $this->assertEquals(0.30, $config->getSizePercentage());
        
        // Test boundary offsets
        $config = new LogoConfig($this->validSvg, offsetX: -1.0, offsetY: 1.0);
        $this->assertEquals(-1.0, $config->getOffsetX());
        $this->assertEquals(1.0, $config->getOffsetY());
        
        // Test boundary padding
        $config = new LogoConfig($this->validSvg, backgroundPadding: 0);
        $this->assertEquals(0, $config->getBackgroundPadding());
        
        $config = new LogoConfig($this->validSvg, backgroundPadding: 10);
        $this->assertEquals(10, $config->getBackgroundPadding());
    }

    public function testConfigurationImmutability(): void
    {
        $config = new LogoConfig(
            logoSource: $this->validSvg,
            sizePercentage: 0.20,
            addBackground: false,
            backgroundColor: '#FF0000',
            backgroundPadding: 5,
            offsetX: 0.3,
            offsetY: -0.2,
            maintainAspectRatio: false
        );
        
        // Verify all properties are set correctly and cannot be changed
        $this->assertEquals($this->validSvg, $config->getLogoSource());
        $this->assertEquals(0.20, $config->getSizePercentage());
        $this->assertFalse($config->shouldAddBackground());
        $this->assertEquals('#FF0000', $config->getBackgroundColor());
        $this->assertEquals(5, $config->getBackgroundPadding());
        $this->assertEquals(0.3, $config->getOffsetX());
        $this->assertEquals(-0.2, $config->getOffsetY());
        $this->assertFalse($config->shouldMaintainAspectRatio());
    }
}
