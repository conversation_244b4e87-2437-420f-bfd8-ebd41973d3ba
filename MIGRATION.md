# Migration Guide: TypeScript to PHP

This guide helps you migrate from the TypeScript QR Code generator to the PHP version. The PHP library maintains API compatibility while adapting to PHP conventions and modern PHP features.

## Quick Reference

| TypeScript | PHP |
|------------|-----|
| `import { qrcodegen } from './qrcodegen'` | `use QrCodeGen\QrCode;` |
| `qrcodegen.QrCode.encodeText()` | `QrCode::encodeText()` |
| `qrcodegen.QrSegment.makeSegments()` | `QrSegment::makeSegments()` |
| `qrcodegen.QrCode.Ecc.MEDIUM` | `Ecc::MEDIUM` |
| `qr.getModule(x, y)` | `$qr->getModule($x, $y)` |

## Namespace Changes

### TypeScript
```typescript
import { qrcodegen } from './qrcodegen';

const QRC = qrcodegen.QrCode;
const qr = QRC.encodeText("Hello", qrcodegen.QrCode.Ecc.MEDIUM);
```

### PHP
```php
use QrCodeGen\QrCode;
use QrCodeGen\Ecc;

$qr = QrCode::encodeText("Hello", Ecc::MEDIUM);
```

## Class and Method Names

### High-Level API

#### Text Encoding
```typescript
// TypeScript
const qr = qrcodegen.QrCode.encodeText("Hello, world!", qrcodegen.QrCode.Ecc.MEDIUM);
```

```php
// PHP
$qr = QrCode::encodeText("Hello, world!", Ecc::MEDIUM);
```

#### Binary Encoding
```typescript
// TypeScript
const data = [72, 101, 108, 108, 111]; // "Hello"
const qr = qrcodegen.QrCode.encodeBinary(data, qrcodegen.QrCode.Ecc.HIGH);
```

```php
// PHP
$data = [72, 101, 108, 108, 111]; // "Hello"
$qr = QrCode::encodeBinary($data, Ecc::HIGH);
```

### Mid-Level API

#### Segment Encoding
```typescript
// TypeScript
const segs = qrcodegen.QrSegment.makeSegments("Hello");
const qr = qrcodegen.QrCode.encodeSegments(segs, qrcodegen.QrCode.Ecc.MEDIUM, 1, 40, -1, true);
```

```php
// PHP
$segs = QrSegment::makeSegments("Hello");
$qr = QrCode::encodeSegments($segs, Ecc::MEDIUM, 1, 40, -1, true);
```

## Error Correction Levels

### TypeScript
```typescript
qrcodegen.QrCode.Ecc.LOW
qrcodegen.QrCode.Ecc.MEDIUM
qrcodegen.QrCode.Ecc.QUARTILE
qrcodegen.QrCode.Ecc.HIGH
```

### PHP
```php
Ecc::LOW
Ecc::MEDIUM
Ecc::QUARTILE
Ecc::HIGH
```

## Segment Creation

### Numeric Segments
```typescript
// TypeScript
const numSeg = qrcodegen.QrSegment.makeNumeric("123456");
```

```php
// PHP
$numSeg = QrSegment::makeNumeric("123456");
```

### Alphanumeric Segments
```typescript
// TypeScript
const alphaSeg = qrcodegen.QrSegment.makeAlphanumeric("HELLO");
```

```php
// PHP
$alphaSeg = QrSegment::makeAlphanumeric("HELLO");
```

### Byte Segments
```typescript
// TypeScript
const byteSeg = qrcodegen.QrSegment.makeBytes([65, 66, 67]);
```

```php
// PHP
$byteSeg = QrSegment::makeBytes([65, 66, 67]);
```

### ECI Segments
```typescript
// TypeScript
const eciSeg = qrcodegen.QrSegment.makeEci(26);
```

```php
// PHP
$eciSeg = QrSegment::makeEci(26);
```

## Property Access

### TypeScript
```typescript
console.log(`Version: ${qr.version}`);
console.log(`Size: ${qr.size}x${qr.size}`);
console.log(`ECC: ${qr.errorCorrectionLevel.ordinal}`);
console.log(`Mask: ${qr.mask}`);
```

### PHP
```php
echo "Version: {$qr->version}\n";
echo "Size: {$qr->size}x{$qr->size}\n";
echo "ECC: {$qr->errorCorrectionLevel->getOrdinal()}\n";
echo "Mask: {$qr->mask}\n";
```

## Module Access

### TypeScript
```typescript
for (let y = 0; y < qr.size; y++) {
    for (let x = 0; x < qr.size; x++) {
        const isDark = qr.getModule(x, y);
        process.stdout.write(isDark ? "██" : "  ");
    }
    console.log();
}
```

### PHP
```php
for ($y = 0; $y < $qr->size; $y++) {
    for ($x = 0; $x < $qr->size; $x++) {
        $isDark = $qr->getModule($x, $y);
        echo $isDark ? "██" : "  ";
    }
    echo "\n";
}
```

## Validation Methods

### TypeScript
```typescript
const isNum = qrcodegen.QrSegment.isNumeric("123456");
const isAlpha = qrcodegen.QrSegment.isAlphanumeric("HELLO");
```

### PHP
```php
$isNum = QrSegment::isNumeric("123456");
$isAlpha = QrSegment::isAlphanumeric("HELLO");
```

## Error Handling

### TypeScript
```typescript
try {
    const qr = qrcodegen.QrCode.encodeText(veryLongText, qrcodegen.QrCode.Ecc.HIGH);
} catch (error) {
    if (error instanceof RangeError) {
        console.error("Data too long:", error.message);
    }
}
```

### PHP
```php
try {
    $qr = QrCode::encodeText($veryLongText, Ecc::HIGH);
} catch (InvalidArgumentException $e) {
    echo "Invalid argument: " . $e->getMessage() . "\n";
} catch (OverflowException $e) {
    echo "Data too long: " . $e->getMessage() . "\n";
}
```

## Advanced Usage Examples

### Manual Segment Optimization

#### TypeScript
```typescript
const segs = [
    qrcodegen.QrSegment.makeAlphanumeric("PRODUCT-"),
    qrcodegen.QrSegment.makeNumeric("12345"),
    qrcodegen.QrSegment.makeBytes([0x20]), // Space
    qrcodegen.QrSegment.makeAlphanumeric("BATCH-"),
    qrcodegen.QrSegment.makeNumeric("67890")
];

const qr = qrcodegen.QrCode.encodeSegments(
    segs,
    qrcodegen.QrCode.Ecc.MEDIUM,
    1,    // minVersion
    10,   // maxVersion
    3,    // mask
    false // boostEcl
);
```

#### PHP
```php
$segs = [
    QrSegment::makeAlphanumeric("PRODUCT-"),
    QrSegment::makeNumeric("12345"),
    QrSegment::makeBytes([0x20]), // Space
    QrSegment::makeAlphanumeric("BATCH-"),
    QrSegment::makeNumeric("67890")
];

$qr = QrCode::encodeSegments(
    $segs,
    Ecc::MEDIUM,
    1,     // minVersion
    10,    // maxVersion
    3,     // mask
    false  // boostEcl
);
```

## Output Generation

### SVG Output

#### TypeScript
```typescript
function toSvgString(qr: qrcodegen.QrCode, border: number): string {
    const size = qr.size + border * 2;
    let svg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${size} ${size}">`;
    svg += `<rect width="100%" height="100%" fill="white"/>`;
    
    for (let y = 0; y < qr.size; y++) {
        for (let x = 0; x < qr.size; x++) {
            if (qr.getModule(x, y)) {
                const px = x + border;
                const py = y + border;
                svg += `<rect x="${px}" y="${py}" width="1" height="1" fill="black"/>`;
            }
        }
    }
    svg += "</svg>";
    return svg;
}
```

#### PHP
```php
function toSvgString(QrCode $qr, int $border): string {
    $size = $qr->size + $border * 2;
    $svg = "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 $size $size\">";
    $svg .= "<rect width=\"100%\" height=\"100%\" fill=\"white\"/>";
    
    for ($y = 0; $y < $qr->size; $y++) {
        for ($x = 0; $x < $qr->size; $x++) {
            if ($qr->getModule($x, $y)) {
                $px = $x + $border;
                $py = $y + $border;
                $svg .= "<rect x=\"$px\" y=\"$py\" width=\"1\" height=\"1\" fill=\"black\"/>";
            }
        }
    }
    $svg .= "</svg>";
    return $svg;
}
```

## Key Differences Summary

1. **Namespaces**: Use `QrCodeGen\` namespace instead of `qrcodegen`
2. **Static methods**: Use `::` instead of `.` for static method calls
3. **Variables**: Use `$` prefix for all variables
4. **Property access**: Use `->` instead of `.` for object properties
5. **Arrays**: PHP arrays work similarly to TypeScript arrays
6. **Enums**: PHP 8.1+ enums instead of TypeScript enum-like classes
7. **Exception types**: PHP exceptions instead of TypeScript Error types
8. **Type hints**: PHP type hints instead of TypeScript type annotations

## Performance Considerations

The PHP version maintains the same algorithmic complexity as the TypeScript version:

- **Memory usage**: Similar memory footprint
- **Generation speed**: Comparable performance for equivalent operations
- **Output quality**: Identical QR codes for the same inputs

## Compatibility Notes

- **PHP Version**: Requires PHP 8.2 or higher
- **Dependencies**: No external dependencies required
- **Output**: Generates identical QR codes to the TypeScript version
- **Standards**: Fully compliant with QR Code Model 2 specification

## Testing Your Migration

To verify your migration is working correctly:

1. Generate QR codes with both versions using identical inputs
2. Compare the resulting module patterns
3. Test with QR code readers to ensure compatibility
4. Validate edge cases and error conditions

The PHP library includes comprehensive tests that validate compatibility with the original TypeScript implementation.
