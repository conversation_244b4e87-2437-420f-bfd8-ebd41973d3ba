<?php

declare(strict_types=1);

// Simple autoloader for testing without composer
spl_autoload_register(function ($class) {
    $prefix = 'QrCodeGen\\';
    $base_dir = __DIR__ . '/src/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

// Include functions
require_once __DIR__ . '/src/functions.php';

use QrCodeGen\QrCode;
use QrCodeGen\QrSegment;
use QrCodeGen\Ecc;

echo "Testing QR Code generation...\n\n";

try {
    // Test 1: Simple text encoding
    echo "Test 1: Encoding simple text 'Hello'\n";
    $qr1 = QrCode::encodeText("Hello", Ecc::LOW);
    echo "Success! Generated QR code with:\n";
    echo "  Version: {$qr1->version}\n";
    echo "  Size: {$qr1->size}x{$qr1->size}\n";
    echo "  Error correction: {$qr1->errorCorrectionLevel->name}\n";
    echo "  Mask: {$qr1->mask}\n";
    
    // Test some modules
    echo "  Sample modules:\n";
    echo "    (0,0): " . ($qr1->getModule(0, 0) ? "dark" : "light") . "\n";
    echo "    (10,10): " . ($qr1->getModule(10, 10) ? "dark" : "light") . "\n";
    echo "\n";

    // Test 2: Numeric encoding
    echo "Test 2: Encoding numeric data '123456789'\n";
    $qr2 = QrCode::encodeText("123456789", Ecc::MEDIUM);
    echo "Success! Generated QR code with:\n";
    echo "  Version: {$qr2->version}\n";
    echo "  Size: {$qr2->size}x{$qr2->size}\n";
    echo "  Error correction: {$qr2->errorCorrectionLevel->name}\n";
    echo "  Mask: {$qr2->mask}\n\n";

    // Test 3: Binary encoding
    echo "Test 3: Encoding binary data\n";
    $binaryData = [72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100]; // "Hello World"
    $qr3 = QrCode::encodeBinary($binaryData, Ecc::HIGH);
    echo "Success! Generated QR code with:\n";
    echo "  Version: {$qr3->version}\n";
    echo "  Size: {$qr3->size}x{$qr3->size}\n";
    echo "  Error correction: {$qr3->errorCorrectionLevel->name}\n";
    echo "  Mask: {$qr3->mask}\n\n";

    // Test 4: Manual segments
    echo "Test 4: Manual segment creation\n";
    $segments = [
        QrSegment::makeNumeric("123"),
        QrSegment::makeAlphanumeric("ABC"),
        QrSegment::makeBytes([33, 34, 35]) // "!\"#"
    ];
    $qr4 = QrCode::encodeSegments($segments, Ecc::QUARTILE);
    echo "Success! Generated QR code with:\n";
    echo "  Version: {$qr4->version}\n";
    echo "  Size: {$qr4->size}x{$qr4->size}\n";
    echo "  Error correction: {$qr4->errorCorrectionLevel->name}\n";
    echo "  Mask: {$qr4->mask}\n\n";

    // Test 5: Different error correction levels
    echo "Test 5: Testing different error correction levels for same data\n";
    $testText = "QR Code test";
    foreach ([Ecc::LOW, Ecc::MEDIUM, Ecc::QUARTILE, Ecc::HIGH] as $ecc) {
        $qr = QrCode::encodeText($testText, $ecc);
        echo "  {$ecc->name}: Version {$qr->version}, Size {$qr->size}x{$qr->size}, Mask {$qr->mask}\n";
    }
    echo "\n";

    // Test 6: Version constraints
    echo "Test 6: Testing version constraints\n";
    $qr6 = QrCode::encodeSegments(
        QrSegment::makeSegments("Test"),
        Ecc::LOW,
        5, // min version
        10, // max version
        -1, // auto mask
        true // boost ECC
    );
    echo "Success! Generated QR code with version constraints:\n";
    echo "  Version: {$qr6->version} (requested range: 5-10)\n";
    echo "  Size: {$qr6->size}x{$qr6->size}\n";
    echo "  Error correction: {$qr6->errorCorrectionLevel->name}\n";
    echo "  Mask: {$qr6->mask}\n\n";

    echo "All QR code generation tests passed!\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Function to display a small QR code as ASCII art (for debugging)
function displayQrCode(QrCode $qr, int $maxSize = 21): void
{
    if ($qr->size > $maxSize) {
        echo "QR code too large to display (size: {$qr->size})\n";
        return;
    }
    
    echo "QR Code (size {$qr->size}x{$qr->size}):\n";
    for ($y = 0; $y < $qr->size; $y++) {
        for ($x = 0; $x < $qr->size; $x++) {
            echo $qr->getModule($x, $y) ? "██" : "  ";
        }
        echo "\n";
    }
    echo "\n";
}

// Display the first QR code if it's small enough
if (isset($qr1) && $qr1->size <= 25) {
    echo "ASCII representation of first QR code:\n";
    displayQrCode($qr1);
}
