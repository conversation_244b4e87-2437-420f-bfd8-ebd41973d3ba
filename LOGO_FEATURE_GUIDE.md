# QR Code Logo Embedding Feature

This guide covers the new logo embedding functionality that allows you to add static SVG logos to the center of generated QR codes while maintaining readability.

## Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [Core Classes](#core-classes)
- [Configuration Options](#configuration-options)
- [Error Correction Considerations](#error-correction-considerations)
- [Output Formats](#output-formats)
- [Advanced Usage](#advanced-usage)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)
- [Examples](#examples)

## Overview

The logo embedding feature provides:

- **SVG Logo Support**: Accept logos as file paths, URLs, or direct SVG strings
- **Precise Positioning**: Center logos with optional offset controls
- **Readability Preservation**: Respect error correction levels and quiet zones
- **Multiple Output Formats**: Native SVG embedding and raster format support
- **Comprehensive Validation**: Input validation and error handling
- **Flexible Configuration**: Extensive customization options

## Quick Start

```php
<?php

use QrCodeGen\QrCode;
use QrCodeGen\QrCodeLogo;
use QrCodeGen\LogoConfig;
use QrCodeGen\Ecc;

// 1. Create a QR code
$qr = QrCode::encodeText("https://example.com", Ecc::MEDIUM);

// 2. Define your logo (SVG string, file path, or URL)
$logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="40" fill="#007bff"/>
    <text x="50" y="55" text-anchor="middle" fill="white" font-size="20">QR</text>
</svg>';

// 3. Create logo configuration
$logoConfig = new LogoConfig(
    logoSource: $logoSvg,
    sizePercentage: 0.15  // 15% of QR code size
);

// 4. Create QR code with logo
$qrWithLogo = new QrCodeLogo($qr, $logoConfig);

// 5. Generate output
$svg = $qrWithLogo->toSvg();
file_put_contents('qr-with-logo.svg', $svg);
```

## Core Classes

### QrCodeLogo

Main class for QR codes with embedded logos.

```php
$qrWithLogo = new QrCodeLogo(QrCode $qrCode, LogoConfig $config);

// Generate different output formats
$svg = $qrWithLogo->toSvg(border: 4, lightColor: '#FFFFFF', darkColor: '#000000');
$png = $qrWithLogo->toPng(scale: 10, border: 4);
$jpeg = $qrWithLogo->toJpeg(scale: 10, border: 4, quality: 90);
```

### LogoConfig

Configuration class for logo embedding options.

```php
$config = new LogoConfig(
    logoSource: string,              // SVG content, file path, or URL
    sizePercentage: float = 0.20,    // Logo size (5%-30% of QR code)
    addBackground: bool = true,      // Add background behind logo
    backgroundColor: string = '#FFFFFF', // Background color
    backgroundPadding: int = 2,      // Background padding in modules
    offsetX: float = 0.0,           // Horizontal offset (-1.0 to 1.0)
    offsetY: float = 0.0,           // Vertical offset (-1.0 to 1.0)
    maintainAspectRatio: bool = true // Maintain logo aspect ratio
);
```

### SvgRenderer

Enhanced SVG renderer with advanced styling options.

```php
$renderer = new SvgRenderer($qrCode, SvgRenderer::STYLE_PATH, $options);

// Different rendering styles
SvgRenderer::STYLE_PATH        // Single path (default)
SvgRenderer::STYLE_RECTANGLES  // Individual rectangles
SvgRenderer::STYLE_ROUNDED     // Rounded rectangles
SvgRenderer::STYLE_DOTS        // Circular dots
```

## Configuration Options

### Logo Sources

**SVG String:**
```php
$logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="40" fill="blue"/>
</svg>';
$config = new LogoConfig($logoSvg);
```

**File Path:**
```php
$config = new LogoConfig('/path/to/logo.svg');
```

**URL:**
```php
$config = new LogoConfig('https://example.com/logo.svg');
```

### Size and Positioning

```php
$config = new LogoConfig(
    logoSource: $logoSvg,
    sizePercentage: 0.20,    // 20% of QR code size
    offsetX: 0.1,            // Shift right by 10%
    offsetY: -0.05           // Shift up by 5%
);
```

### Background Options

```php
$config = new LogoConfig(
    logoSource: $logoSvg,
    addBackground: true,
    backgroundColor: '#F8F9FA',  // Light gray background
    backgroundPadding: 3         // 3 modules padding
);
```

## Error Correction Considerations

Different error correction levels allow different maximum logo sizes:

| Error Correction Level | Recovery Capacity | Max Logo Size |
|----------------------|------------------|---------------|
| LOW                  | ~7%              | 15%           |
| MEDIUM               | ~15%             | 20%           |
| QUARTILE             | ~25%             | 25%           |
| HIGH                 | ~30%             | 30%           |

```php
// Check maximum allowed size
$maxSize = LogoConfig::getMaxLogoSize(Ecc::MEDIUM); // Returns 0.20

// Validation is automatic
$config = new LogoConfig($logoSvg, sizePercentage: 0.25);
$qrWithLogo = new QrCodeLogo($lowEccQr, $config); // Throws LogoException
```

## Output Formats

### SVG Output (Recommended)

```php
$svg = $qrWithLogo->toSvg(
    border: 4,                    // Border size in modules
    lightColor: '#FFFFFF',        // Light module color
    darkColor: '#000000'          // Dark module color
);
```

### PNG Output

```php
// Requires GD extension
$png = $qrWithLogo->toPng(
    scale: 10,                    // Pixels per module
    border: 4                     // Border size in modules
);
```

### JPEG Output

```php
// Requires GD extension
$jpeg = $qrWithLogo->toJpeg(
    scale: 10,                    // Pixels per module
    border: 4,                    // Border size in modules
    quality: 90                   // JPEG quality (0-100)
);
```

## Advanced Usage

### Complex Logos with Gradients

```php
$complexLogo = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
    <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#6f42c1;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#e83e8c;stop-opacity:1" />
        </linearGradient>
    </defs>
    <circle cx="100" cy="100" r="80" fill="url(#grad1)"/>
    <text x="100" y="110" text-anchor="middle" fill="white" font-size="24">LOGO</text>
</svg>';

$config = new LogoConfig($complexLogo, sizePercentage: 0.22);
```

### Enhanced SVG Rendering

```php
$options = [
    'responsive' => true,
    'title' => 'QR Code with Logo',
    'description' => 'QR code containing embedded company logo',
    'class' => 'qr-code-logo',
    'id' => 'main-qr'
];

$renderer = new SvgRenderer($qr, SvgRenderer::STYLE_ROUNDED, $options);
$svg = $renderer->renderWithGradient(
    gradientStart: '#FF0000',
    gradientEnd: '#0000FF',
    gradientDirection: 'diagonal'
);
```

### Preventing Error Correction Boosting

```php
// Disable automatic ECC boosting to test specific levels
$segs = QrSegment::makeSegments("Test data");
$qr = QrCode::encodeSegments($segs, Ecc::LOW, 1, 40, -1, false);
```

## Error Handling

The logo feature uses comprehensive error handling:

```php
use QrCodeGen\Exceptions\LogoException;

try {
    $qrWithLogo = new QrCodeLogo($qr, $config);
    $svg = $qrWithLogo->toSvg();
} catch (LogoException $e) {
    switch ($e->getCode()) {
        case LogoException::INVALID_SOURCE:
            // Handle invalid logo source
            break;
        case LogoException::SIZE_CONSTRAINT_VIOLATION:
            // Handle logo too large for ECC level
            break;
        case LogoException::INVALID_SVG:
            // Handle malformed SVG content
            break;
        case LogoException::INVALID_CONFIGURATION:
            // Handle invalid configuration parameters
            break;
        case LogoException::FILE_SYSTEM_ERROR:
            // Handle file access errors
            break;
        case LogoException::NETWORK_ERROR:
            // Handle URL loading errors
            break;
        case LogoException::SVG_PROCESSING_ERROR:
            // Handle SVG processing errors
            break;
    }
}
```

## Best Practices

### 1. Choose Appropriate Error Correction Levels

```php
// For larger logos, use higher error correction
$qr = QrCode::encodeText($data, Ecc::HIGH);  // Allows up to 30% logo
```

### 2. Optimize Logo Design

- Use simple, high-contrast designs
- Avoid fine details that may not render well
- Test readability with actual QR code scanners
- Consider the final output size

### 3. Validate Input

```php
// Always validate configuration against ECC level
$config->validateForErrorCorrectionLevel($qr->errorCorrectionLevel);
```

### 4. Handle Different Output Scenarios

```php
// Check for GD extension before generating raster formats
if (extension_loaded('gd')) {
    $png = $qrWithLogo->toPng();
} else {
    // Fallback to SVG
    $svg = $qrWithLogo->toSvg();
}
```

### 5. Security Considerations

```php
// The library automatically validates and sanitizes SVG content
// Dangerous elements like <script> tags are rejected
```

## Examples

See the `examples/logo_examples.php` file for comprehensive usage examples including:

- Basic logo embedding
- Custom positioning and sizing
- Different error correction levels
- Complex logos with gradients
- Error handling demonstrations
- Multiple output formats

## Migration from Basic QR Codes

Existing QR code generation remains unchanged. The logo feature is completely optional:

```php
// Existing code continues to work
$qr = QrCode::encodeText("Hello", Ecc::MEDIUM);

// Add logo functionality when needed
$config = new LogoConfig($logoSvg);
$qrWithLogo = new QrCodeLogo($qr, $config);
```

## Performance Considerations

- SVG output is fastest and recommended for web use
- PNG/JPEG generation requires additional processing
- Complex logos with gradients may increase processing time
- File-based logos require disk I/O
- URL-based logos require network requests

## Browser Compatibility

Generated SVG output is compatible with:
- All modern browsers (Chrome, Firefox, Safari, Edge)
- Internet Explorer 9+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Testing

The feature includes comprehensive test coverage:
- Unit tests for all classes and methods
- Integration tests for complete workflows
- Edge case testing for error conditions
- Validation testing for security

Run tests with: `php run_tests_simple.php`
