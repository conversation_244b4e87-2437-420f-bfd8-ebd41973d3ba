<?php

declare(strict_types=1);

// Simple autoloader for testing without composer
spl_autoload_register(function ($class) {
    $prefix = 'QrCodeGen\\';
    $base_dir = __DIR__ . '/src/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

// Include functions
require_once __DIR__ . '/src/functions.php';

use QrCodeGen\QrCode;
use QrCodeGen\Ecc;

echo "Testing pattern drawing functionality...\n\n";

// Test alignment pattern positions using reflection
$reflection = new ReflectionClass(QrCode::class);
$alignMethod = $reflection->getMethod('getAlignmentPatternPositions');
$alignMethod->setAccessible(true);

// Create a test function that simulates the alignment pattern calculation
function testAlignmentPatterns(int $version): array {
    if ($version === 1) {
        return [];
    } else {
        $size = $version * 4 + 17;
        $numAlign = intval($version / 7) + 2;
        $step = intval(($version * 8 + $numAlign * 3 + 5) / ($numAlign * 4 - 4)) * 2;
        $result = [6];
        for ($pos = $size - 7; count($result) < $numAlign; $pos -= $step) {
            array_splice($result, 1, 0, [$pos]);
        }
        return $result;
    }
}

echo "Testing alignment pattern positions:\n";

// Test version 7
$alignPos7 = testAlignmentPatterns(7);
echo "Version 7 alignment positions: [" . implode(', ', $alignPos7) . "]\n";

// Test version 1 (no alignment patterns)
$alignPos1 = testAlignmentPatterns(1);
echo "Version 1 alignment positions: [" . implode(', ', $alignPos1) . "] (expected: empty)\n";

// Test version 40 (maximum alignment patterns)
$alignPos40 = testAlignmentPatterns(40);
echo "Version 40 alignment positions: [" . implode(', ', $alignPos40) . "]\n";
echo "Version 40 alignment count: " . count($alignPos40) . "\n";

// Test data capacity calculations for various versions
echo "\nTesting data capacity calculations:\n";
$getNumDataMethod = $reflection->getMethod('getNumDataCodewords');
$getNumDataMethod->setAccessible(true);

$versions = [1, 5, 10, 20, 30, 40];
$eccLevels = [Ecc::LOW, Ecc::MEDIUM, Ecc::QUARTILE, Ecc::HIGH];

foreach ($versions as $version) {
    echo "Version $version data capacity:\n";
    foreach ($eccLevels as $ecc) {
        $capacity = $getNumDataMethod->invoke(null, $version, $ecc);
        $eccName = $ecc->name;
        echo "  $eccName: $capacity codewords\n";
    }
    echo "\n";
}

echo "Pattern drawing tests completed!\n";
