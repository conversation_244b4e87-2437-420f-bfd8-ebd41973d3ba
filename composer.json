{"name": "qrcodegen/qrcodegen-php", "description": "QR Code generator library for PHP 8.2+ - High-quality port of the TypeScript qrcodegen library", "type": "library", "keywords": ["qr-code", "qrcode", "generator", "barcode", "2d-barcode"], "homepage": "https://github.com/qrcodegen/qrcodegen-php", "license": "MIT", "authors": [{"name": "QR Code Generator PHP Port", "email": "<EMAIL>"}], "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^10.0", "phpstan/phpstan": "^1.10", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"psr-4": {"QrCodeGen\\": "src/"}, "files": ["src/functions.php"]}, "autoload-dev": {"psr-4": {"QrCodeGen\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "phpstan": "phpstan analyse src tests --level=max", "cs-check": "phpcs src tests --standard=PSR12", "cs-fix": "phpcbf src tests --standard=PSR12"}, "config": {"sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}