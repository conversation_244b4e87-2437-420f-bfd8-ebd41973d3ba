<?php

declare(strict_types=1);

/*
 * QR Code generator library (PHP)
 * 
 * Copyright (c) Project Nayuki. (MIT License)
 * https://www.nayuki.io/page/qr-code-generator-library
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * - The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 * - The Software is provided "as is", without warranty of any kind, express or
 *   implied, including but not limited to the warranties of merchantability,
 *   fitness for a particular purpose and noninfringement. In no event shall the
 *   authors or copyright holders be liable for any claim, damages or other
 *   liability, whether in an action of contract, tort or otherwise, arising from,
 *   out of or in connection with the Software or the use or other dealings in the
 *   Software.
 */

namespace QrCodeGen;

use QrCodeGen\Exceptions\LogoException;

/**
 * QR Code with embedded logo functionality.
 * 
 * This class provides the ability to embed SVG logos in the center of QR codes
 * while maintaining readability through error correction level awareness.
 * Supports multiple output formats including SVG and raster formats.
 * 
 * @package QrCodeGen
 * <AUTHOR> Code Generator PHP Port
 * @since 1.1.0
 */
class QrCodeLogo
{
    /**
     * Minimum quiet zone size in modules.
     */
    private const MIN_QUIET_ZONE = 4;

    private QrCode $qrCode;
    private LogoConfig $config;

    /**
     * Creates a new QR code with logo embedding capability.
     *
     * @param QrCode $qrCode The base QR code to embed logo into
     * @param LogoConfig $config Logo configuration options
     * @throws LogoException If configuration is invalid for the QR code
     */
    public function __construct(QrCode $qrCode, LogoConfig $config)
    {
        $this->qrCode = $qrCode;
        $this->config = $config;
        
        // Validate configuration against QR code's error correction level
        $this->config->validateForErrorCorrectionLevel($qrCode->errorCorrectionLevel);
    }

    /**
     * Generates SVG output with embedded logo.
     *
     * @param int $border Border size in modules (minimum 4 for quiet zone)
     * @param string $lightColor Color for light modules (CSS format)
     * @param string $darkColor Color for dark modules (CSS format)
     * @return string Complete SVG markup with embedded logo
     * @throws LogoException If logo processing fails
     */
    public function toSvg(
        int $border = 4,
        string $lightColor = '#FFFFFF',
        string $darkColor = '#000000'
    ): string {
        if ($border < self::MIN_QUIET_ZONE) {
            $border = self::MIN_QUIET_ZONE;
        }

        $size = $this->qrCode->size + $border * 2;
        $logoSvg = $this->config->getLogoSvgContent();
        $logoPosition = $this->calculateLogoPosition($border);
        
        // Start SVG document
        $svg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $svg .= "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n";
        $svg .= "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 $size $size\" stroke=\"none\">\n";
        
        // Background
        $svg .= "\t<rect width=\"100%\" height=\"100%\" fill=\"$lightColor\"/>\n";
        
        // QR code modules
        $svg .= $this->generateQrModules($border, $darkColor, $logoPosition);
        
        // Logo background (if enabled)
        if ($this->config->shouldAddBackground()) {
            $svg .= $this->generateLogoBackground($logoPosition);
        }
        
        // Embedded logo
        $svg .= $this->embedSvgLogo($logoSvg, $logoPosition);
        
        $svg .= "</svg>\n";
        
        return $svg;
    }

    /**
     * Generates PNG output with embedded logo.
     *
     * @param int $scale Pixels per module
     * @param int $border Border size in modules
     * @return string PNG image data as binary string
     * @throws LogoException If GD extension is not available or processing fails
     */
    public function toPng(int $scale = 10, int $border = 4): string
    {
        if (!extension_loaded('gd')) {
            throw LogoException::svgProcessingError('PNG generation requires GD extension');
        }

        // Generate SVG first
        $svgContent = $this->toSvg($border);
        
        // Convert SVG to PNG using GD (simplified approach)
        // For production use, consider using ImageMagick for better SVG support
        return $this->convertSvgToPng($svgContent, $scale, $border);
    }

    /**
     * Generates JPEG output with embedded logo.
     *
     * @param int $scale Pixels per module
     * @param int $border Border size in modules
     * @param int $quality JPEG quality (0-100)
     * @return string JPEG image data as binary string
     * @throws LogoException If GD extension is not available or processing fails
     */
    public function toJpeg(int $scale = 10, int $border = 4, int $quality = 90): string
    {
        if (!extension_loaded('gd')) {
            throw LogoException::svgProcessingError('JPEG generation requires GD extension');
        }

        // Generate PNG first, then convert to JPEG
        $pngData = $this->toPng($scale, $border);
        
        return $this->convertPngToJpeg($pngData, $quality);
    }

    /**
     * Gets the underlying QR code instance.
     *
     * @return QrCode The QR code instance
     */
    public function getQrCode(): QrCode
    {
        return $this->qrCode;
    }

    /**
     * Gets the logo configuration.
     *
     * @return LogoConfig The logo configuration
     */
    public function getConfig(): LogoConfig
    {
        return $this->config;
    }

    /**
     * Calculates the position and size for the logo.
     *
     * @param int $border Border size in modules
     * @return array{x: float, y: float, width: float, height: float} Logo position and dimensions
     */
    private function calculateLogoPosition(int $border): array
    {
        $qrSize = $this->qrCode->size;
        $totalSize = $qrSize + $border * 2;
        
        // Calculate logo size based on percentage
        $logoSize = $qrSize * $this->config->getSizePercentage();
        
        // Calculate center position with offsets
        $centerX = $totalSize / 2;
        $centerY = $totalSize / 2;
        
        $offsetX = $this->config->getOffsetX() * ($qrSize / 4); // Max offset is 1/4 of QR size
        $offsetY = $this->config->getOffsetY() * ($qrSize / 4);
        
        $logoX = $centerX - ($logoSize / 2) + $offsetX;
        $logoY = $centerY - ($logoSize / 2) + $offsetY;
        
        return [
            'x' => $logoX,
            'y' => $logoY,
            'width' => $logoSize,
            'height' => $logoSize,
        ];
    }

    /**
     * Generates QR code modules, excluding the logo area.
     *
     * @param int $border Border size in modules
     * @param string $darkColor Color for dark modules
     * @param array $logoPosition Logo position and dimensions
     * @return string SVG path for QR modules
     */
    private function generateQrModules(int $border, string $darkColor, array $logoPosition): string
    {
        $svg = "\t<path d=\"";
        
        for ($y = 0; $y < $this->qrCode->size; $y++) {
            for ($x = 0; $x < $this->qrCode->size; $x++) {
                if ($this->qrCode->getModule($x, $y)) {
                    $moduleX = $x + $border;
                    $moduleY = $y + $border;
                    
                    // Check if this module overlaps with logo area
                    if (!$this->isModuleInLogoArea($moduleX, $moduleY, $logoPosition)) {
                        $svg .= "M$moduleX,$moduleY h1v1h-1z";
                    }
                }
            }
        }
        
        $svg .= "\" fill=\"$darkColor\"/>\n";
        
        return $svg;
    }

    /**
     * Generates background for the logo area.
     *
     * @param array $logoPosition Logo position and dimensions
     * @return string SVG markup for logo background
     */
    private function generateLogoBackground(array $logoPosition): string
    {
        $padding = $this->config->getBackgroundPadding();
        $x = $logoPosition['x'] - $padding;
        $y = $logoPosition['y'] - $padding;
        $width = $logoPosition['width'] + ($padding * 2);
        $height = $logoPosition['height'] + ($padding * 2);
        $color = $this->config->getBackgroundColor();
        
        return "\t<rect x=\"$x\" y=\"$y\" width=\"$width\" height=\"$height\" fill=\"$color\" rx=\"2\" ry=\"2\"/>\n";
    }

    /**
     * Embeds the SVG logo into the QR code.
     *
     * @param string $logoSvg The SVG logo content
     * @param array $logoPosition Logo position and dimensions
     * @return string SVG markup for embedded logo
     * @throws LogoException If SVG processing fails
     */
    private function embedSvgLogo(string $logoSvg, array $logoPosition): string
    {
        try {
            // Extract viewBox from logo SVG
            $logoViewBox = $this->extractSvgViewBox($logoSvg);
            
            // Extract SVG content (everything between <svg> tags)
            $logoContent = $this->extractSvgContent($logoSvg);
            
            // Create embedded SVG with proper scaling
            $x = $logoPosition['x'];
            $y = $logoPosition['y'];
            $width = $logoPosition['width'];
            $height = $logoPosition['height'];
            
            $svg = "\t<g transform=\"translate($x,$y)\">\n";
            $svg .= "\t\t<svg x=\"0\" y=\"0\" width=\"$width\" height=\"$height\" viewBox=\"$logoViewBox\">\n";
            $svg .= "\t\t\t$logoContent\n";
            $svg .= "\t\t</svg>\n";
            $svg .= "\t</g>\n";
            
            return $svg;
        } catch (\Exception $e) {
            throw LogoException::svgProcessingError('logo embedding', $e);
        }
    }

    /**
     * Checks if a module is within the logo area.
     *
     * @param float $moduleX Module X coordinate
     * @param float $moduleY Module Y coordinate
     * @param array $logoPosition Logo position and dimensions
     * @return bool True if module is in logo area
     */
    private function isModuleInLogoArea(float $moduleX, float $moduleY, array $logoPosition): bool
    {
        $padding = $this->config->getBackgroundPadding();
        
        return $moduleX >= ($logoPosition['x'] - $padding) &&
               $moduleX < ($logoPosition['x'] + $logoPosition['width'] + $padding) &&
               $moduleY >= ($logoPosition['y'] - $padding) &&
               $moduleY < ($logoPosition['y'] + $logoPosition['height'] + $padding);
    }

    /**
     * Extracts viewBox from SVG content.
     *
     * @param string $svgContent The SVG content
     * @return string The viewBox value
     * @throws LogoException If viewBox cannot be extracted
     */
    private function extractSvgViewBox(string $svgContent): string
    {
        if (preg_match('/viewBox=["\']([^"\']+)["\']/', $svgContent, $matches)) {
            return $matches[1];
        }
        
        // Fallback: try to extract width/height
        $width = $height = 100; // Default values
        if (preg_match('/width=["\']([^"\']+)["\']/', $svgContent, $matches)) {
            $width = (float)preg_replace('/[^0-9.]/', '', $matches[1]);
        }
        if (preg_match('/height=["\']([^"\']+)["\']/', $svgContent, $matches)) {
            $height = (float)preg_replace('/[^0-9.]/', '', $matches[1]);
        }
        
        return "0 0 $width $height";
    }

    /**
     * Extracts content from between SVG tags.
     *
     * @param string $svgContent The SVG content
     * @return string The inner SVG content
     * @throws LogoException If content cannot be extracted
     */
    private function extractSvgContent(string $svgContent): string
    {
        if (preg_match('/<svg[^>]*>(.*?)<\/svg>/s', $svgContent, $matches)) {
            return trim($matches[1]);
        }
        
        throw LogoException::svgProcessingError('extracting SVG content');
    }

    /**
     * Converts SVG to PNG using GD (simplified implementation).
     *
     * @param string $svgContent The SVG content
     * @param int $scale Pixels per module
     * @param int $border Border size
     * @return string PNG binary data
     * @throws LogoException If conversion fails
     */
    private function convertSvgToPng(string $svgContent, int $scale, int $border): string
    {
        // This is a simplified implementation
        // For production use, consider using ImageMagick or similar for better SVG support
        $size = ($this->qrCode->size + $border * 2) * $scale;
        
        $image = imagecreate($size, $size);
        if ($image === false) {
            throw LogoException::svgProcessingError('creating PNG image');
        }
        
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        
        if ($white === false || $black === false) {
            imagedestroy($image);
            throw LogoException::svgProcessingError('allocating colors');
        }
        
        imagefill($image, 0, 0, $white);
        
        // Render QR modules (simplified - without logo for now)
        for ($y = 0; $y < $this->qrCode->size; $y++) {
            for ($x = 0; $x < $this->qrCode->size; $x++) {
                if ($this->qrCode->getModule($x, $y)) {
                    $px = ($x + $border) * $scale;
                    $py = ($y + $border) * $scale;
                    imagefilledrectangle($image, $px, $py, $px + $scale - 1, $py + $scale - 1, $black);
                }
            }
        }
        
        ob_start();
        imagepng($image);
        $pngData = ob_get_clean();
        imagedestroy($image);
        
        if ($pngData === false) {
            throw LogoException::svgProcessingError('generating PNG data');
        }
        
        return $pngData;
    }

    /**
     * Converts PNG to JPEG.
     *
     * @param string $pngData PNG binary data
     * @param int $quality JPEG quality
     * @return string JPEG binary data
     * @throws LogoException If conversion fails
     */
    private function convertPngToJpeg(string $pngData, int $quality): string
    {
        $image = imagecreatefromstring($pngData);
        if ($image === false) {
            throw LogoException::svgProcessingError('loading PNG for JPEG conversion');
        }
        
        ob_start();
        imagejpeg($image, null, $quality);
        $jpegData = ob_get_clean();
        imagedestroy($image);
        
        if ($jpegData === false) {
            throw LogoException::svgProcessingError('generating JPEG data');
        }
        
        return $jpegData;
    }
}
